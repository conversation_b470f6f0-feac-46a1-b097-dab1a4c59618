import React, {useState, useEffect, useRef} from 'react';
import {
  Card,
  Table,
  Typography,
  Switch,
  Tooltip,
  Button,
  Input,
  InputNumber,
  Popover,
  Form,
  message,
  Space,
  Modal,
} from 'antd';
import {
  QuestionCircleOutlined,
  DeleteOutlined,
  PlusOutlined,
  SaveOutlined,
  EditOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import {useTranslation} from 'react-i18next';
import './TaskEvaluationDisplayCard.scss';
import {
  createRoleplayTask,
  updateRoleplayTask,
  deleteRoleplayTask,
  createTasksFromPrompt,
} from '@src/app/services/RolePlay/TaskService';
import AntButton from '@src/app/component/AntButton';
import {BUTTON, API} from '@constant';

const {Text, Paragraph} = Typography;
const {TextArea} = Input;

// Editable Cell Component (Slightly modified to handle different input types)
const EditableCell = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  index,
  children,
  handleSave,
  toggleEdit, // Pass toggleEdit to cell
  ...restProps
}) => {
  const inputRef = useRef(null);
  const [form] = Form.useForm();
  const [isEditingThisCell, setIsEditingThisCell] = useState(false);

  useEffect(() => {
    if ((isEditingThisCell || editing) && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditingThisCell, editing]);

  useEffect(() => {
    if (editing && !isEditingThisCell) {
      setIsEditingThisCell(true);
      form.setFieldsValue({[dataIndex]: record[dataIndex]});
    }
  }, [editing, dataIndex, record, form, isEditingThisCell]);

  const startEdit = () => {
    setIsEditingThisCell(true);
    form.setFieldsValue({[dataIndex]: record[dataIndex]});
    if (toggleEdit) toggleEdit(record.key, dataIndex, true); // Notify parent about edit start
  };

  const save = async () => {
    try {
      const values = await form.validateFields();
      setIsEditingThisCell(false);
      if (handleSave) handleSave({...record, ...values});
      if (toggleEdit) toggleEdit(record.key, dataIndex, false); // Notify parent about edit end
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  let childNode = children;

  if (record) {
    // Ensure record exists
    if (editing || isEditingThisCell) {
      if (inputType === 'textarea') {
        childNode = (
          <Form form={form} component={false}>
            <Form.Item
              style={{margin: 0}}
              name={dataIndex}
              rules={[
                {required: title === 'Topic' || title === 'Evaluation guidelines', message: `${title} is required.`},
              ]}
            >
              <TextArea
                ref={inputRef}
                onPressEnter={e => {
                  e.preventDefault();
                  save();
                }}
                onBlur={save}
                autoSize={{minRows: 2, maxRows: 6}}
              />
            </Form.Item>
          </Form>
        );
      } else {
        // Default to Input
        childNode = (
          <Form form={form} component={false}>
            <Form.Item
              style={{margin: 0}}
              name={dataIndex}
              rules={[{required: title === 'Topic', message: `${title} is required.`}]}
            >
              <Input ref={inputRef} onPressEnter={save} onBlur={save} />
            </Form.Item>
          </Form>
        );
      }
    } else {
      childNode = (
        <div className="editable-cell-value-wrap" onClick={startEdit} style={{cursor: 'pointer'}}>
          {children || <Text type="secondary" italic>{`Click to add ${title.toLowerCase()}`}</Text>}
        </div>
      );
    }
  }

  return <td {...restProps}>{childNode}</td>;
};

export const TaskEvaluationDisplayCard = ({
  dataSource = [],
  onTaskAdd,
  onTaskUpdate,
  onTaskDelete,
  courseId, // Nhận courseId từ parent component
  ...restProps
}) => {
  const {t} = useTranslation();
  const [editingCellKey, setEditingCellKey] = useState(''); // To track which cell is being edited e.g. "rowKey-dataIndex"
  const [editedRows, setEditedRows] = useState({}); // Track modified rows with their data
  const [newRows, setNewRows] = useState({}); // Track new rows that need to be created
  const [newTaskId, setNewTaskId] = useState(null); // Keep track of the most recently added task
  const [isAIModalVisible, setIsAIModalVisible] = useState(false); // Trạng thái hiển thị modal tạo từ AI
  const [userPrompt, setUserPrompt] = useState(''); // Prompt người dùng nhập vào
  const [isGeneratingWithAI, setIsGeneratingWithAI] = useState(false); // Trạng thái đang tạo từ AI

  // Kiểm tra xem một cell có đang ở chế độ chỉnh sửa không
  const isCellEditing = (record, dataIndex) => {
    return editingCellKey === `${record._id}-${dataIndex}` || record._id === newTaskId;
  };

  const handleToggleEdit = (rowKey, dataIndex, isEditing) => {
    setEditingCellKey(isEditing ? `${rowKey}-${dataIndex}` : '');

    // Mark row as edited
    if (isEditing) {
      const task = dataSource.find(item => item._id === rowKey);
      if (task) {
        setEditedRows(prev => ({
          ...prev,
          [rowKey]: {...prev[rowKey], ...task},
        }));
      }
    }
  };

  const handleSave = updatedRecord => {
    // Xử lý helpfulLinks nếu là chuỗi (người dùng nhập vào)
    if (typeof updatedRecord.helpfulLinks === 'string') {
      updatedRecord.helpfulLinks = updatedRecord.helpfulLinks
        .split(',')
        .map(link => link.trim())
        .filter(Boolean);
    }

    onTaskUpdate(updatedRecord);

    // Check if this is a new row (has a temp id) or an existing one
    if (updatedRecord._id.toString().startsWith('temp_')) {
      setNewRows(prev => ({
        ...prev,
        [updatedRecord._id]: {...prev[updatedRecord._id], ...updatedRecord},
      }));
    } else {
      setEditedRows(prev => ({
        ...prev,
        [updatedRecord._id]: {...prev[updatedRecord._id], ...updatedRecord},
      }));
    }

    // Clear newTaskId if this is the task that was just added
    if (updatedRecord._id === newTaskId) {
      setNewTaskId(null);
    }
  };

  const handleRowSave = async record => {
    try {
      console.log('record', record);
      if (record._id.toString().startsWith('temp_')) {
        // New row - create
        const dataToSave = {
          ...record,
          courseId, // Đảm bảo courseId được truyền vào khi tạo task mới
        };

        // Đảm bảo helpfulLinks luôn là mảng
        if (typeof dataToSave.helpfulLinks === 'string') {
          dataToSave.helpfulLinks = dataToSave.helpfulLinks
            .split(',')
            .map(link => link.trim())
            .filter(Boolean);
        } else if (!Array.isArray(dataToSave.helpfulLinks)) {
          dataToSave.helpfulLinks = [];
        }

        delete dataToSave._id; // Remove temp id
        delete dataToSave.key; // Remove key

        console.log('Saving new task:', dataToSave);
        try {
          const createdTask = await createRoleplayTask(dataToSave);
          if (!createdTask) {
            return;
          }

          // Update the task in parent component with the real _id from server
          // Truyền original_temp_id để parent có thể tìm và thay thế đúng task
          onTaskUpdate({...createdTask, original_temp_id: record._id, key: createdTask._id});

          // Remove from newRows state
          const updatedNewRows = {...newRows};
          delete updatedNewRows[record._id];
          setNewRows(updatedNewRows);

          // ALSO Remove from editedRows state if it exists for the tempId
          if (editedRows[record._id]) {
            const updatedEditedRows = {...editedRows};
            delete updatedEditedRows[record._id];
            setEditedRows(updatedEditedRows);
          }
        } catch (error) {
          console.error('Error creating task:', error);
          message.error(t('CREATE_ERROR', 'Failed to create task'));
        }
      } else {
        // Existing row - update
        const dataToUpdate = {...record};

        // Đảm bảo helpfulLinks luôn là mảng
        if (typeof dataToUpdate.helpfulLinks === 'string') {
          dataToUpdate.helpfulLinks = dataToUpdate.helpfulLinks
            .split(',')
            .map(link => link.trim())
            .filter(Boolean);
        } else if (!Array.isArray(dataToUpdate.helpfulLinks)) {
          dataToUpdate.helpfulLinks = [];
        }

        console.log('Updating task:', dataToUpdate);
        try {
          await updateRoleplayTask(dataToUpdate);
          message.success(t('TASK_UPDATED', 'Task updated successfully'));
        } catch (error) {
          console.error('Error updating task:', error);
          message.error(t('UPDATE_ERROR', 'Failed to update task'));
        }

        // Remove from editedRows state
        const updatedEditedRows = {...editedRows};
        delete updatedEditedRows[record._id];
        setEditedRows(updatedEditedRows);
      }
    } catch (error) {
      console.error('Error saving task:', error);
      message.error(t('SAVE_ERROR', 'Failed to save task'));
    }
  };

  const handleAddTask = () => {
    const newId = `temp_${Date.now()}`;
    const newTask = {
      _id: newId,
      name: '',
      description: '',
      evaluationGuidelines: '',
      weight: 0,
      exampleVideoUrl: '',
      helpfulLinks: [],
      isMakeOrBreak: false,
    };

    onTaskAdd(newTask);

    // Add to newRows
    setNewRows(prev => ({
      ...prev,
      [newTask._id]: newTask,
    }));

    // Set this as the new task being edited
    setNewTaskId(newId);
  };

  // Hiển thị modal nhập prompt AI
  const showAIModal = () => {
    setIsAIModalVisible(true);
    setUserPrompt('');
  };

  // Đóng modal nhập prompt AI
  const handleAIModalCancel = () => {
    setIsAIModalVisible(false);
    setUserPrompt('');
  };

  // Xử lý tạo task từ AI
  const handleGenerateWithAI = async () => {
    if (!courseId) {
      message.error(t('COURSE_ID_REQUIRED', 'Course ID is required'));
      return;
    }

    if (!userPrompt.trim()) {
      message.error(t('PROMPT_REQUIRED', 'Please enter a prompt'));
      return;
    }

    try {
      setIsGeneratingWithAI(true);
      const result = await createTasksFromPrompt(courseId, userPrompt.trim());

      if (result && result.tasks && Array.isArray(result.tasks)) {
        // Thêm các task mới vào danh sách
        const newTasks = result.tasks.map((task, index) => {
          const newId = `temp_${Date.now()}_${index}`;
          return {
            ...task,
            _id: newId,
            key: newId,
          };
        });

        // Thêm các task mới vào state
        const newRowsToAdd = {};
        newTasks.forEach(task => {
          newRowsToAdd[task._id] = task;
        });

        setNewRows(prev => ({
          ...prev,
          ...newRowsToAdd,
        }));

        // Thêm các task mới vào danh sách hiển thị
        newTasks.forEach(task => {
          onTaskAdd(task);
        });

        message.success(t('TASKS_GENERATED', 'Tasks generated successfully'));
        setIsAIModalVisible(false);
      } else {
        message.error(t('GENERATE_ERROR', 'Failed to generate tasks'));
      }
    } catch (error) {
      console.error('Error generating tasks:', error);
      message.error(t('GENERATE_ERROR', 'Failed to generate tasks'));
    } finally {
      setIsGeneratingWithAI(false);
    }
  };

  const handleDeleteTask = async taskId => {
    try {
      // Nếu là task tạm thời (chưa lưu vào DB)
      if (taskId.toString().startsWith('temp_')) {
        onTaskDelete(taskId);
        return;
      }

      // Hiển thị modal xác nhận xóa
      Modal.confirm({
        title: t('CONFIRM_DELETE_TASK', 'Are you sure you want to delete this task?'),
        content: t('DELETE_TASK_WARNING', 'This action cannot be undone.'),
        okText: t('DELETE', 'Delete'),
        okType: 'danger',
        cancelText: t('CANCEL', 'Cancel'),
        onOk: async () => {
          try {
            await deleteRoleplayTask(taskId);
            message.success(t('TASK_DELETED', 'Task deleted successfully'));
            onTaskDelete(taskId);
          } catch (error) {
            console.error('Error deleting task:', error);
            message.error(t('DELETE_ERROR', 'Failed to delete task'));
          }
        },
      });
    } catch (error) {
      console.error('Error in handleDeleteTask:', error);
      message.error(t('DELETE_ERROR', 'Failed to delete task'));
    }
  };

  const columns = [
    {
      title: (
        <Tooltip title={t('TASK_TOPIC_TOOLTIP', 'The main subject.')}>
          <Text strong>{t('TOPIC_COLUMN', 'Topic')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'name',
      key: 'name',
      width: '12%',
      editable: true,
      inputType: 'textarea',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{`Click to add ${t('TOPIC_COLUMN', 'Topic')}`}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('DESCRIPTION_TOOLTIP', 'Brief description of the task.')}>
          <Text strong>{t('DESCRIPTION_COLUMN', 'Description')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'description',
      key: 'description',
      width: '15%',
      editable: true,
      inputType: 'textarea',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{`Click to add ${t('DESCRIPTION_COLUMN', 'Description')}`}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('EVALUATION_GUIDELINES_TOOLTIP', 'Criteria for evaluation.')}>
          <Text strong>{t('EVALUATION_GUIDELINES_COLUMN', 'Evaluation guidelines')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'evaluationGuidelines',
      key: 'evaluationGuidelines',
      width: '28%',
      editable: true,
      inputType: 'textarea',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{`Click to add ${t(
            'EVALUATION_GUIDELINES_COLUMN',
            'Evaluation guidelines',
          )}`}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('WEIGHT_TOOLTIP', 'Importance percentage.')}>
          <Text strong>{t('WEIGHT_COLUMN', 'Weight')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'weight',
      key: 'weight',
      width: '10%',
      render: (text, record) => (
        <InputNumber
          min={0}
          max={100}
          value={text} // Use value instead of defaultValue for controlled component behavior
          formatter={value => `${value}%`}
          parser={value => String(value).replace('%', '')}
          onChange={value => {
            const updatedRecord = {...record, weight: value};
            handleSave(updatedRecord);
          }}
          style={{width: '100%', maxWidth: '80px'}}
        />
      ),
    },
    {
      title: (
        <Tooltip title={t('EXAMPLE_VIDEOS_TOOLTIP', 'Link to example video.')}>
          <Text strong>{t('EXAMPLE_VIDEOS_COLUMN', 'Example videos')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'exampleVideoUrl',
      key: 'exampleVideoUrl',
      width: '12%',
      editable: true,
      inputType: 'textarea',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{`Click to add ${t('HELPFUL_LINKS_COLUMN', 'Helpful links')}`}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('HELPFUL_LINKS_TOOLTIP', 'Link to helpful resources. Separate multiple links with commas.')}>
          <Text strong>{t('HELPFUL_LINKS_COLUMN', 'Helpful links')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'helpfulLinks',
      key: 'helpfulLinks',
      width: '12%',
      editable: true,
      inputType: 'textarea',
      render: links => {
        if (!links || (Array.isArray(links) && links.length === 0)) {
          return (
            <Text type="secondary" italic>
              Enter links separated by commas
            </Text>
          );
        }
        return Array.isArray(links) ? links.join(', ') : links;
      },
    },
    {
      title: (
        <Tooltip title={t('MAKE_OR_BREAK_TOOLTIP', 'Critical for passing.')}>
          <Text strong>{t('MAKE_OR_BREAK_COLUMN', 'Make or Break')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'isMakeOrBreak',
      key: 'isMakeOrBreak',
      width: '8%',
      align: 'center',
      render: (isMakeOrBreak, record) => (
        <Switch
          checked={isMakeOrBreak}
          onChange={checked => {
            const updatedRecord = {...record, isMakeOrBreak: checked};
            handleSave(updatedRecord);
          }}
        />
      ),
    },
    {
      title: t('ACTIONS_COLUMN', 'Actions'),
      key: 'actions',
      width: '8%',
      align: 'center',
      render: (_, record) => {
        const isEdited = editedRows[record._id] || newRows[record._id];

        return (
          <Space>
            <Tooltip title={t('SAVE_TASK_TOOLTIP', 'Save this task/topic')}>
              <Button
                type="primary"
                size="small"
                icon={<SaveOutlined />}
                onClick={() => handleRowSave(record)}
                disabled={!isEdited}
              />
            </Tooltip>
            <Tooltip title={t('DELETE_TASK_TOOLTIP', 'Delete this task/topic')}>
              <Button
                type="text"
                danger
                size="small"
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteTask(record._id)}
              />
            </Tooltip>
          </Space>
        );
      },
    },
  ];

  const mergedColumns = columns.map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: record => ({
        record,
        inputType: col.inputType || 'input',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isCellEditing(record, col.dataIndex),
        handleSave: handleSave,
        toggleEdit: handleToggleEdit,
      }),
    };
  });

  return (
    <Card className="task-evaluation-display-card" bordered={false} style={restProps.style}>
      <Space style={{marginBottom: 16, marginLeft: 16, marginTop: 16, justifyContent: 'space-between', alignSelf:'end'}}>
        <AntButton onClick={handleAddTask} size={'large'} icon={<PlusOutlined />} type={BUTTON.DEEP_NAVY}>
          {t('ADD_TASK_TOPIC', 'Add Topic / Task')}
        </AntButton>
        <AntButton
          onClick={showAIModal}
          icon={<RobotOutlined />}
          disabled={!courseId}
          type={'primary'}
          style={{background: '#722ED1', marginRight: 8}}
          size={'large'}
        >
          {t('CREATE_FROM_AI', 'Create from AI')}
        </AntButton>
      </Space>

      {/* Modal nhập prompt AI */}
      <Modal
        title={t('CREATE_TASKS_FROM_AI', 'Create Tasks from AI')}
        open={isAIModalVisible}
        onCancel={handleAIModalCancel}
        footer={[
          <AntButton key="cancel" onClick={handleAIModalCancel} size={'large'}>
            {t('CANCEL', 'Cancel')}
          </AntButton>,
          <AntButton
            key="generate"
            type="primary"
            loading={isGeneratingWithAI}
            onClick={handleGenerateWithAI}
            style={{background: '#722ED1'}}
            size={'large'}
          >
            {t('GENERATE', 'Generate')}
          </AntButton>,
        ]}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('PROMPT_LABEL', 'Describe the tasks you want to generate')}
            help={t('PROMPT_HELP', 'For example: "conversation between a student and a potential customer"')}
          >
            <TextArea
              rows={4}
              value={userPrompt}
              onChange={e => setUserPrompt(e.target.value)}
              placeholder={t('PROMPT_PLACEHOLDER', 'Enter your prompt here...')}
            />
          </Form.Item>
        </Form>
      </Modal>

      <div className="table-container">
        <Table
          components={{
            body: {
              cell: EditableCell,
            },
          }}
          rowClassName="editable-row" // No function needed if it's just a class name
          columns={mergedColumns}
          dataSource={dataSource.map(task => ({...task, key: task._id}))} // Ensure key is present
          pagination={false}
          // scroll={{y: 400}}
          className="evaluation-table"
          bordered
        />
      </div>
    </Card>
  );
};

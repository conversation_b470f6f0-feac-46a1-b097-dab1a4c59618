import { connect } from "react-redux";
import clsx from "clsx";
import { useTranslation } from "react-i18next";

import AntButton from "@component/AntButton";

import { BUTTON, VISIBLE_TOOL, TYPE_OF_TOOL } from "@constant";

import { favoriteTool, unFavoriteTool } from "@services/Tool";

import HEARD_ICON from "@src/asset/icon/heart/heart.svg";
import HEARD_EMPTY_ICON from "@src/asset/icon/heart/heart-empty.svg";
import ArrowUpRight from "@component/SvgIcons/ArrowUpRight";
import INPUT_YOUTUBE from "@src/asset/inputType/input-youtube.svg";
import INPUT_DOCUMENT from "@src/asset/inputType/input-document.svg";
import INPUT_IMAGE from "@src/asset/inputType/input-image.svg";
import INPUT_AUDIO from "@src/asset/inputType/input-audio.svg";

import * as tool from "@src/ducks/tool.duck";

import "./ToolGrid.scss";
import { useEffect, useMemo } from "react";

const ToolGrid = ({
                    toolLoading = false,
                    toolItemData,
                    onShowPreview,
                    hanldeAfterFavoriteTool,
                    toolData,
                    ...props
                  }) => {
  const { t } = useTranslation();
  
  const { isFavorite, visible } = useMemo(() => toolItemData, [toolItemData]);
  const isComingSoon = useMemo(() => visible === VISIBLE_TOOL.developing.value, [visible]);
  
  const isDisabled = useMemo(() => {
    return isComingSoon || (!!toolLoading && toolLoading !== toolItemData.toolId);
  }, [isComingSoon, toolLoading, toolItemData]);
  
  const onFavoriteTool = async (toolId, isFavorite) => {
    let dataResponse;
    if (isFavorite) {
      dataResponse = await unFavoriteTool(toolId, true);
    } else {
      dataResponse = await favoriteTool(toolId, true);
    }
    if (dataResponse) {
      if (toolData) props.updateFavTool(toolId, !isFavorite);
      if (hanldeAfterFavoriteTool) hanldeAfterFavoriteTool(toolId, dataResponse.isFavorite);
    }
  };
  
  const generateToolIcon = (type) => {
    const iconMap = {
      [TYPE_OF_TOOL.VIDEO]: INPUT_YOUTUBE,
      [TYPE_OF_TOOL.IMAGE]: INPUT_IMAGE,
      [TYPE_OF_TOOL.AUDIO]: INPUT_AUDIO,
    };
    
    return <img className="tool-item__icon" src={iconMap[type] || INPUT_DOCUMENT} alt="" />;
  };
  
  const isShowVideoPreview = toolItemData?.linkYoutube && !isComingSoon;
  
  return <div key={toolItemData._id} className="tool-item col-span-1">
    <div className="tool-item__content">
      <div className="tool-item__category">{t(toolItemData.categories?.trim()?.toUpperCase())}</div>
      <div className="tool-item__title">
        {generateToolIcon(toolItemData?.inputType)}
        <div>{toolItemData?.name}</div>
      </div>
      <div className="tool-item__description">{toolItemData?.description}</div>
    </div>
    <div className="tool-item__footer">
      {isShowVideoPreview && <div
        className={"tool-item__video-preview"}
        onClick={onShowPreview}
      >
        {t("HOW_TO_USE")}
      </div>}
      <AntButton
        block
        size="large"
        type={BUTTON.DEEP_NAVY}
        className="tool-item__access-tool"
        onClick={() => props.handleAccessTool(toolItemData._id)}
        icon={!isComingSoon && <ArrowUpRight />}
        loading={toolLoading === toolItemData.toolId}
        disabled={isDisabled}
      >
        {t(isComingSoon ? "COMING_SOON" : "USE_TOOL")}
      </AntButton>
    </div>
    
    {!isComingSoon && <img
      src={isFavorite ? HEARD_ICON : HEARD_EMPTY_ICON}
      alt=""
      className={clsx("tool-item__favorite", { "favorited": isFavorite })}
      onClick={() => onFavoriteTool(toolItemData._id, isFavorite)}
    />}
  </div>;
};


function mapStateToProps(store) {
  const { toolData } = store.tool;
  return { toolData };
}

const mapDispatchToProps = {
  ...tool.actions,
};

export default connect(mapStateToProps, mapDispatchToProps)(ToolGrid);


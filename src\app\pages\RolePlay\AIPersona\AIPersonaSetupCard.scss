.ai-persona-setup-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);

  .ant-card-body {
    padding: 20px;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0; // Divider will provide space
  }

  &__main-title {
    margin-bottom: 0 !important; // Override Ant Design\'s default margin
    font-size: 18px; // Match image closer
    font-weight: 600;
  }

  .ant-divider-horizontal {
    margin: 16px 0;
  }

  &__section {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }

  &__section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  &__section-title {
    margin-bottom: 0 !important; // Override Ant Design\'s default margin
    font-size: 16px; // Match image closer
    font-weight: 600;
  }

  &__edit-button {
    padding: 0; // Make it more like a plain link
    font-size: 14px;
    height: auto; // Adjust height to content
  }

  &__details-content {
    display: flex;
    align-items: flex-start; // Align items to the top of the flex container
    gap: 16px;
  }

  &__avatar {
    border: 1px solid #f0f0f0; // Subtle border for avatar
    border-radius: 8px; // Match card rounding
  }

  &__details-text {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &__name {
    font-size: 16px;
    font-weight: 600;
  }

  .ant-space-item {
    line-height: 1.6; // Improve readability for list of character traits
    .ant-typography strong {
        color: #595959; // Slightly lighter color for labels
    }
  }

  &__paragraph {
    color: #595959; // Consistent text color for paragraphs
    line-height: 1.6;
    margin-bottom: 0; // Remove default paragraph margin if inside a section
  }
}

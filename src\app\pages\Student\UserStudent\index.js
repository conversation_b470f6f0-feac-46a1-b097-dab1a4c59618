import { useEffect, useState, useMemo } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import dayjs from "dayjs";
import moment from "moment";
import { usePageViewTracker } from "@src/ga";

import { toast } from "@component/ToastProvider";

import { formatDate } from "@common/functionCommons";

import { BUTTON, CONSTANT, PACKAGE_TYPE, USER_TYPE, WORKSPACE_TYPE } from "@constant";
import { LINK } from "@link";

import { requestForgetPassword, updateInfoUser } from "@services/Auth";
import RenameDescription from "@app/component/RenameDescription";

import EditPen from "@src/app/component/SvgIcons/Edit/EditPen";

import * as auth from "@src/ducks/auth.duck";

import "./UserStudent.scss";
import ModalChangePassword from "@src/app/pages/User/ModalChangePassword";
import AntButton from "@src/app/component/AntButton";
import { getCurrentPackage } from "@src/app/services/Subscription";
import Loading from "@src/app/component/Loading";

const UserStudent = ({ availableWorkspaces, user, ...props }) => {
  usePageViewTracker("User");
  const { t } = useTranslation();

  const [packageData, setPackageData] = useState([]);
  const [isShowModalRename, setIsShowModalRename] = useState(false);
  const [isShowChangePassword, setShowChangePassword] = useState(false);
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    getPackageData();
  }, []);

  const onChangePassword = async () => {
    if (user?.hasPassword) {
      onToggleChangePassword();
      return;
    }
    const apiResponse = await requestForgetPassword({ email: user.email });
    if (apiResponse) {
      toast.success(t("AUTH_MESSAGE_FORGOT_PASSWORD_SUCCESS").format(t("YOUR_EMAIL_ADDRESS")), { unique: true });
    }
  };

  const submitNameUser = async (newUserName) => {
    const apiResponse = await updateInfoUser({ fullName: newUserName });
    if (apiResponse) {
      props.requestUser();
      toast.success("UPDATE_NAME_SUCCESS");
      setIsShowModalRename(false);
    }
  };

  const getPackageData = async () => {
    setLoading(true);
    const apiResponse = await getCurrentPackage();
    if (apiResponse) {
      setPackageData(apiResponse);
    }
    setLoading(false);
  };

  const onToggleChangePassword = () => {
    setShowChangePassword((pre) => !pre);
  };

  // Kiểm tra điều kiện hiển thị nút "Renew Now"
  const shouldShowRenewButton = (packageItem) => {
    const { endDate, remainingSpeaking, remainingWriting, packageId, isFree } = packageItem || {};
    const isBasePackage = packageId?.type === PACKAGE_TYPE.BASE.value;
    const remainingDays = dayjs(endDate).diff(dayjs(), "day");
    if (isFree) {
      return false;
    }
    // Điều kiện cho gói tháng
    if (isBasePackage) {
      // Nếu số lượt còn lại ≤ 3, hiển thị thông báo ngay
      // if ((!!remainingSpeaking && remainingSpeaking <= 3) || (!!remainingWriting && remainingWriting <= 3)) {
      //   return true;
      // }

      // Nếu số lượt > 3 nhưng gói tháng còn 3 ngày, hiển thị thông báo
      if (remainingDays <= 3) {
        return true;
      }
    }
    // Điều kiện cho gói lượt
    else {
      // Hiển thị thông báo khi còn 3 lượt chấm
      const totalRemaining = (remainingSpeaking || 0) + (remainingWriting || 0);
      if (totalRemaining <= 3) {
        return true;
      }
    }

    return false;
  };

  return (
    <Loading id="account-student" active={isLoading}>
      <div className="account__content">
        <div className="account__content__item">
          <div className="item__label">{t("NAME")}</div>
          <div className="item__value item__value-name">
            {user?.fullName}
            <div className="edit-icon" onClick={() => setIsShowModalRename(true)}>
              <EditPen />
            </div>
          </div>
        </div>
        <div className="account__content__item">
          <div className="item__label">{t("EMAIL")}</div>
          <div className="item__value">{user?.email}</div>
        </div>
        <div className="account__content__item">
          <div className="item__label">{t("PASSWORD")}</div>
          <div className="item__value password">
            {user?.hasPassword && <div className="asterisks-password">*****************</div>}
            <div className="link-change-password" onClick={onChangePassword}>
              {t(user?.hasPassword ? "CHANGE_PASSWORD" : "SET_PASSWORD")}
            </div>
          </div>
        </div>
        <div className="account__content__item">
          <div className="item__label">{t("CURRENT_PACKAGE")}</div>
          <div className="item__value item__value-packages">
            {packageData?.map((item, index) => {
              const isBasePackage = item?.packageId?.type === PACKAGE_TYPE.BASE.value;
              const usingPrice = item?.packageId?.prices?.find(
                (price) => price?.unitName === item.unitPrice && parseInt(price?.intervalCount) === item.intervalCount
              );
              const { endDate, remainingSpeaking, remainingWriting } = item || {};
              const remainingDays = dayjs(endDate).diff(dayjs(), "day");

              // Sử dụng hàm kiểm tra điều kiện hiển thị nút "Renew Now"
              const isRenew = shouldShowRenewButton(item);

              return (
                <div key={index} className="item__value-packages__item">
                  <div className="package-name">
                    <span className="package-name__text">{item?.packageId?.name}</span>
                    {item?.packageId?.paidType?.toUpperCase() === CONSTANT.FREE && (
                      <Link to={LINK.PRICING}>
                        <AntButton className="btn-upgrade" type={BUTTON.DEEP_GREEN} size="small">
                          {t("UPGRADE")}
                        </AntButton>
                      </Link>
                    )}
                    {isRenew && (
                      <Link to={LINK.PAYMENT_ID.format(item?.packageId?._id)} state={{ ...usingPrice }}>
                        <AntButton className="btn-upgrade" type={BUTTON.DEEP_GREEN} size="small">
                          {t("RENEW_NOW")}
                        </AntButton>
                      </Link>
                    )}
                  </div>
                  <div className="package-term">
                    <ul>
                      {isBasePackage && (
                        <li>
                          {t("EXPRIED")}: {formatDate(item?.endDate)}
                        </li>
                      )}
                      <li>{t(isBasePackage ? "REMAINING_MONTHLY_SUBMISSION" : "REMAINING_SUBMISSION")}:</li>
                    </ul>
                  </div>
                  <div className="package-quota">
                    {(!!item.remainingWriting || item.remainingWriting === 0) && (
                      <span className="package-quota__item">
                        {t("WRITING")}: {item.remainingWriting}
                      </span>
                    )}
                    {(!!item.remainingSpeaking || item.remainingSpeaking === 0) && (
                      <span className="package-quota__item">
                        {t("SPEAKING")}: {item.remainingSpeaking}
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
      <RenameDescription
        isShowModal={isShowModalRename}
        initialValue={user?.fullName}
        title={t("EDIT_NAME")}
        handleClose={() => setIsShowModalRename(!isShowModalRename)}
        handleSubmit={submitNameUser}
        required
      />
      <ModalChangePassword open={isShowChangePassword} onCancel={onToggleChangePassword} />
    </Loading>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  ...auth.actions,
};

export default connect(mapStateToProps, mapDispatchToProps)(UserStudent);

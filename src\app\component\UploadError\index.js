import React from "react";
import { useTranslation } from "react-i18next";

import AntButton from "@component/AntButton";

import { BUTTON } from "@constant";

import AlertCircleIcon from "@component/SvgIcons/AlertCircleIcon";

import "./UploadError.scss";

function UploadError({ content, onCancel, onTryAgain }) {
  const { t } = useTranslation();
  
  if (!content) return null;
  
  return <div className="upload-error-container">
    <div className="upload-error__content">
      <div className="upload-error__title">
        <AlertCircleIcon />
        <span>{t("OOPS_WE_ARE_SORRY")}</span>
      </div>
      <div className="upload-error__error-text">
        {content}
      </div>
    </div>
    <div className="upload-error__action">
      <AntButton size="large" onClick={onCancel}>
        {t("CANCEL")}
      </AntButton>
      <AntButton size="large" type={BUTTON.LIGHT_GREEN} onClick={onTryAgain}>
        {t("TRY_AGAIN")}
      </AntButton>
    </div>
  </div>;
}

export default UploadError;
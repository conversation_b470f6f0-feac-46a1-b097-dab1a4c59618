:root {
  --project-content__value-input-bg: #FFF;
  --account__content-item-color: #000;
  --divider-bg: #E6E6E6;
}

[data-theme='dark'] {
  --project-content__value-input-bg: rgba(56, 53, 53, 0.50);
  --account__content-item-color: #FFF;
  --divider-bg: #343333;
}

#account {
  display: flex;
  flex-direction: column;
  width: 792px;
  gap: 24px;
  margin: auto;
  max-width: 100%;

  @media screen and (max-width: 900px) {
    width: 450px;
  }

  .account__content {
    width: 100%;
    display: flex;
    padding: 24px 32px;
    flex-direction: column;
    align-items: flex-start;
    border-radius: 8px;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.10);
    background: var(--project-content__value-input-bg);

    .account__content__item {
      display: flex;
      align-items: flex-start;
      flex-wrap: wrap;
      color: var(--account__content-item-color);
      font-size: 16px;
      width: 100%;

      &:not(:last-child) {
        &::after {
          content: '';
          width: 100%;
          height: 1px;
          background: var(--divider-bg);
          margin: 24px 0;
        }
      }

      .item__label {
        font-weight: 600;
        width: 200px;

        @media screen and (max-width: 900px) {
          width: 120px;
        }
      }

      .item__value {
        font-weight: 400;
        word-break: break-word;


        &.password {
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          .link-change-password {
            color: var(--typo-colours-support-blue);
            cursor: pointer;
          }
        }

        &.item__value-workspace {
          display: flex;
          flex-direction: column;
          gap: 4px;


          .description-input {
            display: flex;
            max-width: 100%;
            gap: 8px;
            align-items: flex-start;

            &.description-input-nodata {
              cursor: pointer;
              display: flex;

              color: var(--typo-colours-support-blue);

              svg path {
                stroke: var(--typo-colours-support-blue);
              }
            }

            .description-input__value {
              display: flex;
              align-items: center;
              height: 18px;


            }

          }
        }

        &.item__value-subscription {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .subscription-package-name {
            display: flex;

            span {
              padding: 6px 16px;
              border-radius: 4px;
              background: #DCFEDB;
              color: var(--typo-colours-support-green);
            }
          }

          .subscription-package-term {
            display: flex;
            gap: 8px;
            align-items: center;

            .ellipse {
              width: 4px;
              height: 4px;
              background-color: #000000;
              border-radius: 50%;
              flex-shrink: 0;
            }

            .subscription-package-term-end {
              color: var(--typo-colours-support-blue);
            }
          }
        }

        .link-payment-history {
          color: var(--typo-colours-support-blue);
        }
      }
    }

    .item-editing-input {
      display: flex;
      flex-direction: row;
      gap: 18px;
    }
  }

  .btn-upgrade {
    text-align: center;

    button {
      padding: 10px 32px 10px 32px;
      border-radius: 8px;
      font-weight: 600;
      height: auto;
    }
  }

  .account__content__item__name {
    display: flex;
    flex-direction: row;
    align-items: center !important;
    justify-content: space-between;
  }

  .account__content__item__name__left {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .edit-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    cursor: pointer;

    svg {
      width: 18px;
      height: 18px;

      path {
        stroke-width: 1;
      }
    }
  }
}

.chart-remaning-container {
  display: flex;
  flex-direction: row;
  gap: 24px;
  font-family: Segoe UI;

  .chart-remaning-item {
    display: flex;

    flex-direction: column;
    width: 50%;
    padding: 24px;
    box-shadow: var(--shadow-level-2);
    border-radius: 8px;
    gap: 32px;
    background-color: var(--background-light-background-2);
  }

  .chart-remaning-info {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  .chart-remaning-info__left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .chart-remaning-info__right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
  }

  .chart-remaning-info__title {
    font-size: 16px;
    line-height: 20px;
    font-weight: 600;
  }

  .chart-remaning-info__description {
    font-weight: 400;
    font-size: 14px;
    line-height: 17.5px;
    color: var(--typo-colours-support-blue-light)
  }

  .chart-remaning-info__avalibled {
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
  }

  .chart-remaning-info__exp {
    display: flex;
    flex-direction: row;
  }

  .chart-remaning-info__exp {
    font-weight: 400;
    font-size: 14px;
    line-height: 17.5px;
    color: var(--typo-colours-support-blue-light)
  }

  .chart-remaning-info__expTitle {
    color: var(--support-colours-red)
  }

  .chart-remaning-info__exp-link {
    color: var(--typo-colours-support-blue);
    cursor: pointer;
  }

}
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import RenameDescription from "@src/app/component/RenameDescription";

import { toast } from "@component/ToastProvider";

import PlusIcon from "@component/SvgIcons/PlusIcon";
import EditPen from "@src/app/component/SvgIcons/Edit/EditPen";
import clsx from "clsx";

function DescriptionWorkspace({ value, onSubmit }) {
  const { t } = useTranslation();

  const [isShowModal, setShowModal] = useState(false);

  function handleToggleModal() {
    setShowModal(!isShowModal);
  }

  async function handleSubmit(description) {
    const isSuccess = await onSubmit(description);
    if (isSuccess) {
      toast.success("UPDATE_WORKSPACE_DESCRIPTION_SUCCESS");
      handleToggleModal();
    }
  }

  return (<>
    <div
      className={clsx("description-input", { "description-input-nodata": !value })}
      {...(value ? {} : { onClick: handleToggleModal })}
    >
      {!value ? <>
        <PlusIcon />
        {t("ADD_DESCRIPTION_WORKSPACE")}
      </>
        : <>
          <div className="description-input__text">{value}</div>
          <div className="edit-icon" onClick={handleToggleModal}>
            <EditPen />
          </div>
        </>
      }
    </div >
    <RenameDescription
      isShowModal={isShowModal}
      initialValue={value}
      title={t('DESCRIPTION_WORKSPACE')}
      handleClose={handleToggleModal}
      handleSubmit={handleSubmit}
      placeholder={t("ADD_DESCRIPTION_WORKSPACE")}
    />
  </>);
}

export default DescriptionWorkspace;
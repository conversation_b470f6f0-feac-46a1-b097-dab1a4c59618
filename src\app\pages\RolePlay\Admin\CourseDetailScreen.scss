.course-detail-container {
  .course-detail-header-card {
    margin-bottom: 24px;

    .course-detail-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .course-detail-title {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }

      .course-detail-description {
        margin: 4px 0 0;
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }

  .course-detail-form-card {
    .ant-card-head-title {
      font-weight: 600;
    }

    .ant-upload-picture-card-wrapper {
      // display: inline-block;
    }
    .ant-upload.ant-upload-select-picture-card {
      width: 104px;
      height: 104px;
      margin-right: 8px;
      margin-bottom: 8px;
      background-color: #fafafa;
      border: 1px dashed #d9d9d9;
      border-radius: 2px;
      cursor: pointer;
      transition: border-color 0.3s;
      &:hover {
        border-color: #1890ff;
      }
      .ant-upload-disabled {
        cursor: not-allowed;
      }
    }
    .ant-upload-list-picture-card .ant-upload-list-item {
        width: 104px;
        height: 104px;
        padding: 0;
    }

    .ant-upload-list-item-info {
        > span {
            display: block;
            width: 100%;
            height: 100%;
        }
        img {
            object-fit: cover;
        }
    }

  }

  .btn-cancel {
    // Custom styles if needed
  }

  .btn-save {
    min-width: 100px;
  }
}

.reference-materials-section {
  .reference-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .reference-url-list,
  .reference-file-list {
    margin-bottom: 24px;
  }

  .reference-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 8px 12px;
    background-color: #f5f5f5;
    border-radius: 4px;

    a {
      color: #1890ff;
      text-decoration: none;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
    }

    .reference-actions {
      display: flex;
      align-items: center;
    }

    .reference-view-btn {
      color: #52c41a;
      margin-right: 4px;
    }

    .reference-delete-btn {
      color: #ff4d4f;
    }
  }

  .add-reference-button {
    margin-top: 16px;
  }
}

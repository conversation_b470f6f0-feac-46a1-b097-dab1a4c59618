.courses-management-container {
  .courses-management-info-card,
  .courses-management-search-card,
  .courses-management-table-card {
    margin-bottom: 24px;
  }

  .courses-management-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .courses-management-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }

    .courses-management-description {
      margin: 4px 0 0;
      color: rgba(0, 0, 0, 0.65);
    }

    .btn-create-course {
      margin-left: auto;
    }
  }

  .form-filter {
    .search-form-item {
      margin-bottom: 0;
    }

    .search-buttons-col {
      .search-buttons {
        display: flex;
        gap: 8px;
        justify-content: flex-end;

        .ant-btn {
          min-width: 100px;
        }
      }
    }
  }

  .courses-management-table-card {
    .ant-table {
      .ant-table-container {
        .ant-table-thead > tr > th {
          background: #fafafa;
        }
      }
    }

    .course-actions {
      display: flex;
      gap: 8px;
      justify-content: center;

      .btn-edit-course,
      .btn-delete-course {
        border: none;
        background: none;
        color: #1890ff;
        transition: color 0.3s;

        &:hover {
          color: #40a9ff;
        }
      }

      .btn-delete-course {
        color: #ff4d4f;

        &:hover {
          color: #ff7875;
        }
      }
    }

    .course-title-value {
      color: #1890ff;
      transition: color 0.3s;

      &:hover {
        color: #40a9ff;
      }
    }
  }
}

import React, { useLayoutEffect, useRef } from "react";
import { <PERSON>lide<PERSON> } from "antd";
import { connect } from "react-redux";

import { convertSecondToHHMMSS, convertSecondToMs } from "@common/functionCommons";

import "./TimelineSlider.scss";

function TimelineSlider({ maxDurationSeconds, ...props }) {
  const sliderRef = useRef();
  
  const { value, maxSlider, disabled } = props;
  
  const [valueStart, valueEnd] = value;
  
  useLayoutEffect(() => {
    if (sliderRef.current) {
      const sliderTrack = sliderRef.current.querySelector(".ant-slider-track");
      const timeRanger = convertSecondToMs(Math.abs(value[0] - value[1]));
      
      const span = document.createElement("span");
      span.style.setProperty("font-size", "12px");
      span.style.setProperty("font-weight", "600");
      const node = document.createTextNode(timeRanger);
      span.appendChild(node);
      document.body.appendChild(span);
      const spanWidth = span.getBoundingClientRect().width;
      document.body.removeChild(span);
      
      
      sliderTrack.setAttribute("time-ranger", sliderTrack.offsetWidth > spanWidth + 5 ? timeRanger : "");
    }
  }, [value]);
  
  
  return <div ref={sliderRef} className="js-timeline timeline-container">
    <div className="timeline__time">00:00:00</div>
    <Slider
      abcde="value"
      disabled={!maxSlider || disabled}
      className="timeline__slider"
      min={0}
      max={maxSlider}
      //max={videoDuration.videoLength}
      onChange={([start, end]) => {
        const dataReturn = { start, end };
        if (end - start <= maxDurationSeconds) {
          props.onChange(dataReturn);
        } else if (valueEnd - valueStart < maxDurationSeconds) {
          if (valueStart === start) {
            dataReturn.end = start + maxDurationSeconds;
          } else if (valueEnd === end) {
            dataReturn.start = end - maxDurationSeconds;
          }
          props.onChange(dataReturn);
        }
      }}
      value={value}
      step={1}
      range={{ draggableTrack: true }}
      tooltip={{
        formatter: convertSecondToHHMMSS,
      }}
    />
    <div className="timeline__time">
      {convertSecondToHHMMSS(maxSlider)}
    </div>
  </div>;
}

function mapStateToProps(store) {
  const { maxDurationSeconds } = store.app;
  return { maxDurationSeconds };
}

export default connect(mapStateToProps)(TimelineSlider);
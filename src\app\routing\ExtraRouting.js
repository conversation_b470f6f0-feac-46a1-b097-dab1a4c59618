import { useEffect } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import axios from "axios";

import { toast } from "@component/ToastProvider";

import { ERROR_STATUS_MESSAGE } from "@src/constants/error";

import * as auth from "@src/ducks/auth.duck";


const ExtraRouting = ({ ...props }) => {
  const { t } = useTranslation();
  const location = useLocation();
  
  
  useEffect(() => {
    scrollUp();
  }, [location]);
  
  function scrollUp() {
    const jsContent = document.getElementById("js-layout-content");
    if (jsContent) {
      jsContent.scrollTo({ top: 0, behavior: "smooth" });
    }
  }
  
  axios.interceptors.response.use(
    function (response) {
      // Do something with response data
      return response;
    },
    function (error) {
      // Do something with response error
      if (error.response && !error?.config?.hideNoti) {
        let message = typeof error?.response?.data?.message === "string"
          ? error?.response?.data?.message
          : "";
        
        if (!message && ERROR_STATUS_MESSAGE[error?.response?.status]) {
          message = t(ERROR_STATUS_MESSAGE[error.response.status]);
        }
        toast.error(message, { replace: true });
      }
      
      return Promise.reject(error);
    },
  );
  
  
  return <></>;
};

function mapStateToProps() {
  return {};
}

const mapDispatchToProps = {
  ...auth.actions,
};

export default connect(mapStateToProps, mapDispatchToProps)(ExtraRouting);
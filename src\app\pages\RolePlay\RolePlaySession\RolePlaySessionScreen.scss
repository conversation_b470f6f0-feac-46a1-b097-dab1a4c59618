.role-play-session-screen {
  padding: 16px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  &__content-container {
    flex: 1;
    overflow-y: auto;
    width: 100%;
    max-width: 1200px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80vh;
    width: 100%;
  }

  &__start-task-button {
    width: 100%;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    border: none;

  }

  &__introduction {
    width: 100%;
    margin: 0 auto 24px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

    .role-play-session-screen__header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      width: 100%;
      padding-bottom: 10px;
      border-bottom: 1px solid #eaeaea;

      .role-play-session-screen__back-button {
        flex-shrink: 0;
      }

      .role-play-session-screen__title {
        flex-grow: 1;
        text-align: center;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }
    }
  }

  &__session-list {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  &__title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
  }

  &__task-info {
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
  }

  &__task-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
  }

  &__task-description {
    font-size: 14px;
    margin-bottom: 10px;
  }

  &__estimated-time {
    font-size: 14px;
    color: #666;
  }

  &__persona-info {
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f0f7ff;
    border-radius: 8px;
  }

  &__persona-name {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 10px;
  }

  &__persona-details {
    font-size: 14px;

    p {
      margin-bottom: 5px;
    }
  }

  &__warning {
    margin-bottom: 20px;
    padding: 10px;
    background-color: #fff9e6;
    border-left: 4px solid #ffc53d;
    border-radius: 4px;
  }

  &__start-button {
    display: block;
    margin: 0 auto;
    padding: 0 30px;
    height: 40px;
  }

  &__content {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  &__task-selection-area {
    display: flex;
    flex-direction: row;
    gap: 24px;
    padding: 16px;
    align-items: flex-start;
    width: 100%;
    margin-bottom: 24px;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  &__task-selection-component-wrapper {
    flex: 3;
    min-width: 0;
  }

  &__completed-sessions-component-wrapper {
    flex: 1;
    min-width: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    border-bottom: 1px solid #eaeaea;
  }

  &__course-info {
    display: flex;
    flex-direction: column;
  }

  &__course-title {
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
  }

  &__pass-score {
    font-size: 14px;
    color: #666;
  }

  &__video-area {
    display: flex;
    width: 100%;
    margin-bottom: 30px;
    height: 400px;
    gap: 20px;
    justify-content: center;
  }

  &__participant-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #fff;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
    max-width: 50%;
  }

  &__participant-video {
    width: 100%;
    height: calc(100% - 90px);
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #000;
    border-radius: 4px;
  }

  &__participant-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__video-element {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__visualizer-wrapper {
    width: 100%;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 5px;
    margin-bottom: 5px;
  }

  &__participant-name {
    margin-top: 0;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    text-align: center;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__transcript {
    width: 100%;
    padding: 15px;
    margin: 0 0 20px 0;
    font-size: 16px;
    text-align: center;
  }

  &__current-message {
    font-size: 18px;
    font-weight: 400;
    color: #333;

    &.interrupted {
      color: #ff7875;
      opacity: 0.8;
    }
  }

  &__controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    gap: 10px;
    flex-direction: column;
    align-self: center;
    width: 100%;
    max-width: 600px;
  }

  &__timer-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
  }

  &__timer {
    font-size: 24px;
    font-weight: 700;
    color: #333;
  }

  &__total-duration {
    font-size: 14px;
    font-weight: 500;
    color: #666;
  }

  &__mic-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__mic-button {
    margin-bottom: 5px;
    padding: 0;
    border: none;
    background: transparent ;
    height: auto;
    border-radius: 20px;

    &:hover {
      background: transparent;
    }

    img {
      transition: all 0.3s ease;
      border-radius: 20px;
    }
  }

  &__mic-status {
    font-size: 12px;
    text-align: center;
    color: #666;
  }

  &__end-button {
    font-weight: 500;
    border-radius: 8px;
  }

  &__record-controls {
    position: fixed;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;

    &.recording {
      .role-play-session-screen__record-button {
        animation: pulse 1.5s infinite;
      }
    }
  }

  &__record-button {
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .session-introduction-layout {
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: start;
    gap: 20px;

    > :first-child {
      flex: 1;
    }

    > :last-child {
      width: 300px;
    }
  }
}

.role-play-session-screen__ai-processing-indicator {
  font-style: italic;
  color: #666;
  margin-top: 5px;
}

.role-play-session-screen__ai-speaking-indicator {
  font-style: italic;
  color: #52c41a;
  margin-top: 5px;

  .role-play-session-screen__audio-progress {
    margin-top: 8px;
    width: 200px;
    margin-left: auto;
    margin-right: auto;
  }
}

.role-play-session-screen__ai-interrupted-indicator {
  font-style: italic;
  color: #ff7875;
  margin-top: 5px;
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}

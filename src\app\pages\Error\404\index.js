import { connect } from "react-redux";


import "./Error404.scss";
import { usePageViewTracker } from "@src/ga";

function Error404({ ...props }) {
  usePageViewTracker("Error404");
  return (
    <div id="404" className="error-page">
      <h1>404</h1>
      <p>Xin lỗi, trang bạn tìm kiếm không tồn tại.</p>
      <a href="/" className="home-link">Quay lại trang chủ</a>
    </div>
  );
}

function mapStateToProps(store) {
  return {};
}


export default connect(mapStateToProps)(Error404);

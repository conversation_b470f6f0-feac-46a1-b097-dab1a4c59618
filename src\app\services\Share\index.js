import { API } from "@api";
import axios from "axios";

import { convertSnakeCaseToCamelCase } from "@common/dataConverter";
import { genQueryParam } from "@src/common/functionCommons";
import { createBase, deleteBase, getAllBase, updateBase, getAllManager, getDetailBase } from "@services/Base";


export function getAllSharedWithMe(query, loading) {
  const config = { loading };
  const queryParams = genQueryParam(query);
  return axios
    .get(`${API.SHARED_WITH_ME}?${queryParams}`, config)
    .then(response => {
      if (response.status === 200) return convertSnakeCaseToCamelCase(response?.data);
      return null;
    })
    .catch((err) => {
      //renderMessageError(err);
      return null;
    });
}

export function getAllUserAccess(query, loading) {
  return getAllBase(API.SHARE, query, ["userId"], loading);
}

export function updateUserAccess(shareData, params) {
  const api = !!shareData.projectId ? API.SHARE_PROJECT : !!shareData.folderId ? API.SHARE_FOLDER : !!shareData.sessionId ? API.SHARE_SESSION : API.SHARE_WORKSPACE;
  return createBase(api, shareData, [], false, true, params);
}

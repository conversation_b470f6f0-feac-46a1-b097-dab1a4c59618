#tools {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;

  .search-wrapper{
    width: 100%;
    display: grid;
    gap: 6px;

    @media screen and (min-width: 1535.98px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    @media screen and (min-width: 768px) and (max-width: 1535.98px) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    @media screen and (max-width: 767.98px) {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }

  .tools__group-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;

    button {
      border-radius: 4px;

      >span {
        display: flex;
        gap: 4px;
      }
    }
  }
}
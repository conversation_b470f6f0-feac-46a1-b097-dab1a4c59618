import {API} from '@constants/api';
import {getAllPaginationBaseList, getDetailBaseWithParamsNew} from '../Base';

/**
 * Fetch all courses
 * @param {Object} query - Query parameters for filtering
 * @param {Object} paging - Paging parameters for filtering
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @returns {Promise} - Promise resolving to the courses data
 */
export const getAllCourses = (query = {}, paging = {page:1, limit:12}, populateOpts = [], loading = true) => {
  return getAllPaginationBaseList(API.COURSES, paging, query, populateOpts, loading);
};

/**
 * Fetch details for a specific course
 * @param {String} id - The ID of the course
 * @param {Array} populateOpts - Population options for related data
 * @param {Boolean} loading - Whether to show loading state
 * @param {Boolean} toastError - Whether to show error toast
 * @returns {Promise} - Promise resolving to the course details
 */
export const getCourseDetails = (id, populateOpts = [], loading = true, toastError = false) => {
  const config = { loading, toastError };
  return axios.get(API.COURSE_DETAILS.format(id), config)
  .then(response => {
    if (response.status === 200) return response;
    return null;
  })
  .catch(err => {
    console.log("err", err);
    return null;
  });
};

.payment {
  display: flex;
  gap: 24px;
  justify-content: center;
  padding: 32px 0;
  background: radial-gradient(18.71% 33.59% at 50% 8.03%, #F4F3FF 0.01%, #FFFFFF 100%);
  box-shadow: 0px 4px 20px 0px #0000001A;
  border-radius: 24px;

  .payment__body {
    display: flex;
    justify-content: space-between;
    width: 747px;

    .payment__process-bar {

      .process-bar__step {
        display: flex;
        flex-direction: column;

        .step-content {
          display: flex;
          gap: 16px;
          align-items: center;

          .step-content__icon {
            background-color: #ECF0F4;
            color: var(--typo-colours-support-blue-light);
          }

          .step-content__label {
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            text-align: left;
          }
        }

        .step-line {
          width: 1px;
          height: 40px;
          background-color: var(--button-stroke-normal);
          margin-left: 16px;
        }

        &.process-bar__step-active {
          .step-content__icon {
            background-color: #3A18CE;
            color: #FFFFFF;
          }

          .step-content__label {
            color: #3A18CE;
          }
        }

        &.process-bar__step-done {
          .step-line {
            background-color: #26D06D;
          }

          .step-content__icon {
            background-color: #E5FFF3;
          }

          .step-content__label {
            color: #26D06D;
          }
        }
      }
    }

    .payment__content {
      width: 448px;
      padding: 24px;
      background-color: var(--background-light-background-2);
      border-radius: 8px;
      border: 1px solid var(--button-stroke-normal);
      height: fit-content;

      .payment__content__payment-package {
        display: flex;
        flex-direction: column;

        .payment-package-item {
          display: flex;
          flex-direction: column;

          &::after {
            content: '';
            height: 1px;
            width: 100%;
            background-color: var(--background-light-background-grey);
            margin: 24px 0;
          }

          .payment-package-item__label {
            display: flex;
            flex-direction: row;
            gap: 8px;
            align-items: center;
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
          }

          .payment-package-info {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .payment-package-info__name {
              display: flex;
              flex-direction: row;
              gap: 8px;
              align-items: center;
            }

            .payment-package-info__name,
            .payment-package-info__description {
              padding-left: 32px;
            }

            .payment-package-info__name {
              .payment-package-info__package-duaration {
                padding: 2px 8px;
                border-radius: 4px;
                background-color: #3A18CE;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
              }
            }

            .payment-package-info__date {
              font-weight: 500;
            }
          }

          .payment-package-discount {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .payment-package-discount__discount-input {
              display: flex;
              width: 100%;
              gap: 15px;

              .ant-input-affix-wrapper {
                padding: 8px 10px;

                .ant-input-suffix {
                  img {
                    opacity: 0.8;
                    cursor: pointer;
                  }
                }
              }

              input {
                border-radius: 5px;

                &:not(:focus) {
                  border-color: var(--background-light-background-grey);
                }
              }

              &.payment-package-discount__discount-input--invalid {
                .ant-input-affix-wrapper {
                  border-color: #FF0307;
                }
              }
            }

            .payment-package-discount__notification {
              display: flex;
              gap: 4px;
              align-items: flex-start;
              font-size: 14px;
              line-height: 20px;

              .payment-package-discount__policy-link {
                color: var(--typo-colours-support-blue);
              }
            }

            .payment-package-discount__discount-list {
              display: flex;
              gap: 8px;

              .discount-list__discount-item {
                display: flex;
                gap: 4px;
                border-radius: 4px;
                padding: 2px 4px;
                background-color: #F4EFFF;
                font-size: 14px;
                color: #3A18CE;

                img {
                  padding: 0 3px;
                  cursor: pointer;
                }
              }
            }
          }

          .payment-package-calculate {
            display: flex;
            flex-direction: column;
            gap: 21px;

            .payment-package-calculate__data {
              display: flex;
              justify-content: space-between;

              &.payment-package-calculate__total {
                border-radius: 4px;
                padding: 10px 16px;
                font-weight: 600;
                background-color: #E7E5FF;
                color: #3A18CE;
              }

              &.payment-package-calculate__discount {
                color: var(--typo-colours-support-blue-light);

                .payment-package-item__label {
                  font-weight: 400;
                }
              }
            }
          }

          .payment-package-method {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .ant-space {
              row-gap: 16px;
            }
          }

          .payment-package-recurring {
            display: flex;
            flex-direction: column;
            gap: 16px;
          }

          .ant-radio-wrapper {
            &:not(.ant-radio-wrapper-disabled) {
              .ant-radio-inner {
                border-color: var(--typo-colours-primary-black);
              }
            }

            &.ant-radio-wrapper-checked {
              color: #2ABD76;

              .ant-radio-inner {
                border-color: var(--support-colours-green-dark);
                background-color: #FFFFFF;

                &:after {
                  background-color: var(--support-colours-green-dark);
                  transform: scale(0.5);
                }
              }
            }
          }
        }

        .payment-package-actions {
          display: flex;
          gap: 32px;
          justify-content: center;
        }
      }

      .payment__content__payment-complete {
        display: flex;
        gap: 16px;
        flex-direction: column;
        align-items: center;
        text-align: center;
        color: var(--primary-colours-blue-navy);

        img {
          width: 97px;
          height: 120px;
        }

        .payment-complete__notification__fail {
          color: var(--support-colours-red);
        }

        .payment-complete__hotline {
          font-weight: 700;
        }
      }

      .payment__content__processing {
        display: flex;
        flex-direction: column;
        gap: 16px;
        align-items: center;
        text-align: center;

        .loading-spin {
          width: 24px;
          height: 24px;
        }
      }
    }
  }
}
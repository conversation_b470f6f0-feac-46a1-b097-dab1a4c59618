import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import NoData from "@component/NoData";
import TemplateItem from "./TemplateItem";
import RenameTemplate from "./RenameTemplate";
import PreviewTemplate from "./PreviewTemplate";

import { cloneObj } from "@common/functionCommons";
import ArrowUpRight from "@component/SvgIcons/ArrowUpRight";

import "./TemplateList.scss";
import clsx from "clsx";

function TemplateList({ ...props }) {
  const { t } = useTranslation();
  const { dataSource, onShowMore, onRename, onUseTemplate, onDelete, showUpdate, useTemplateLabel, 
    className, isShowModified = true } = props;

  const [stateTemplate, setStateTemplate] = useState({
    isShowModal: false,
    templateSelected: null,
  });

  const [statePreview, setStatePreview] = useState({
    isShowModal: false,
    templateSelected: null,
  });

  function handleShowModal(isShowModal, templateSelected = null) {
    if (isShowModal) {
      setStateTemplate({ isShowModal, templateSelected });
    } else {
      setStateTemplate(prevState => Object.assign({}, prevState, { isShowModal }));
    }
  }

  function handleShowPreview(isShowModal, templateSelected = null) {
    if (isShowModal) {
      setStatePreview({ isShowModal, templateSelected });
    } else {
      setStatePreview(prevState => Object.assign({}, prevState, { isShowModal }));
    }
  }

  function handleUpdateTemplate(dataForm) {
    const dataRequest = cloneObj(dataForm);
    dataRequest._id = stateTemplate.templateSelected._id;
    onRename(dataRequest)
      .then(() => handleShowModal(false));
  }


  return <>
    {(!props.children && !dataSource?.length) && <NoData />}
    <div className={clsx("template-list", className)}>
      {props.children}
      {dataSource?.map((template, index) => {
        return <TemplateItem
          key={template._id}
          templateData={template}
          onUseTemplate={onUseTemplate}
          onRename={onRename}
          onDelete={onDelete}
          handleShowModal={handleShowModal}
          handleShowPreview={handleShowPreview}
          showUpdate={showUpdate}
          isShowModified={isShowModified}
        />;
      })}

      {!!onShowMore && <div className="template-item template-item__show-more" onClick={onShowMore}>
        {t("SHOW_MORE")}
        <ArrowUpRight />
      </div>}

    </div>

    <RenameTemplate
      isOpen={stateTemplate.isShowModal}
      templateSelected={stateTemplate.templateSelected}
      handleOk={handleUpdateTemplate}
      handleCancel={() => handleShowModal(false)}
    />

    <PreviewTemplate
      isOpen={statePreview.isShowModal}
      templateSelected={statePreview.templateSelected}
      handleOk={handleUpdateTemplate}
      handleCancel={() => handleShowPreview(false)}
      onUseTemplate={onUseTemplate}
      useTemplateLabel={useTemplateLabel}
    />
  </>;
}

export default TemplateList;
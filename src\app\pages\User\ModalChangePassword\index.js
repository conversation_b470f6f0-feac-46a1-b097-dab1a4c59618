import { useEffect, useState } from 'react';
import { Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';

import AntModal from '@src/app/component/AntModal';
import { AntForm } from '@src/app/component/AntForm';
import AntButton from '@src/app/component/AntButton';

import { BUTTON } from '@constant';

import { changePassword } from '@src/app/services/Auth';
import { toast } from '@src/app/component/ToastProvider';

import './ModalChangePassword.scss';

const ModalChangePassword = ({ open, onCancel }) => {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      form.resetFields();
    }
  }, [open]);

  async function onChangePassword(values) {
    setLoading(true);
    const dataRequest = { oldPassword: values.oldPassword.trim(), newPassword: values.newPassword.trim() };
    const apiResponse = await changePassword(dataRequest);
    if (apiResponse) {
      toast.success("CHANGE_PASSWORD_SUCCESS");
      onCancel();
    }
    setLoading(false);
  }

  const handleFocus = (fieldName) => {
    form.setFields([{
      name: fieldName,
      errors: [],
    }]);
  }
  return (
    <AntModal
      width={500}
      className="modal-change-password"
      footerless
      open={open}
      onCancel={onCancel}
      closable={false}
    >
      <div className="auth-content__title">{t("CHANGE_PASSWORD")}</div>

      <AntForm
        form={form}
        layout="vertical"
        onFinish={onChangePassword}
        className="auth-content__form"
      >
        <AntForm.Item
          name="oldPassword"
          validateTrigger="onBlur"
          rules={[() => ({
            validator(_, value) {
              if (!value || !value?.trim()) {
                return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
              }
              return Promise.resolve();
            },
          }),]}
        >
          <Input.Password size="large" placeholder={t("OLD_PASSWORD")} onFocus={() => handleFocus("password")} />
        </AntForm.Item>
        <AntForm.Item
          name="newPassword"
          validateTrigger="onBlur"
          rules={[() => ({
            validator(_, value) {
              if (!value || !value?.trim()) {
                return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
              }
              if (value.trim().length >= 6) {
                return Promise.resolve();
              }
              return Promise.reject(new Error(t("MINIMUM_6_CHARACTERS_PASSWORD")));
            },
          }),]}
        >
          <Input.Password size="large" placeholder={t("NEW_PASSWORD")} onFocus={() => handleFocus("password")} />
        </AntForm.Item>

        <AntForm.Item
          name="confirm"
          dependencies={["newPassword"]}
          validateTrigger="onBlur"
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value) {
                  return Promise.reject(new Error(t("CAN_NOT_BE_BLANK")));
                }
                if (getFieldValue("newPassword") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(t("VALIDATE_MESSAGE_RE_ENTER_NEW_PASSWORD")));
              },
            }),
          ]}
        >
          <Input.Password size="large" placeholder={t("RE_ENTER_NEW_PASSWORD")} onFocus={() => handleFocus("confirm")} />
        </AntForm.Item>
      </AntForm>

      <AntButton
        block
        size="large"
        className="auth-content__submit"
        type={BUTTON.DEEP_NAVY}
        onClick={() => form.submit()}
        loading={isLoading}
      >
        {t("CHANGE_PASSWORD")}
      </AntButton>
    </AntModal >
  )
};

export default ModalChangePassword;
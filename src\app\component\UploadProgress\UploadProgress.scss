.upload-progress-container {
  background-color: var(--primary-colours-blue-navy-light-1);
  padding: 48px 24px;

  .upload-progress-wrapper {
    display: flex;
    gap: 16px;

    .upload-progress__file {
      display: flex;
      flex-direction: row;
      padding: 12.5px 16px;
      gap: 10px;
      flex: 1;
      min-height: 55px;
      align-items: center;
      background-color: var(--white);
      border-radius: 8px;

      .upload-progress__file-icon {
        display: flex;
        align-items: center;
        width: 32px;

        img {
          width: 32px;
          height: 32px;
        }
      }

      .upload-progress__file-upload {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 11px;

        .upload-progress__file-info {
          flex: 1;
          display: flex;
          flex-direction: row;
          gap: 16px;
          justify-content: space-between;

          .upload-progress__file-name {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .upload-progress__file-capacity {
            float: right;
            color: var(--typo-colours-support-blue-light);
            text-wrap: nowrap;
          }
        }

        .upload-progress__file-progress {
          height: 24px;
        }
      }
    }

    .upload-progress__close {
      align-content: center;

      .ant-btn {
        box-shadow: var(--shadow-level-2);
      }
    }
  }
}

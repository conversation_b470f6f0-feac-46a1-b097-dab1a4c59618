import { useEffect, useState, useMemo } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";
import dayjs from "dayjs";
import moment from "moment";
import { usePageViewTracker } from "@src/ga";

import { toast } from "@component/ToastProvider";

import AntButton from "@component/AntButton";
import DescriptionWorkspace from "./DescriptionWorkspace";
import { formatDate } from "@common/functionCommons";

import { BUTTON, CONSTANT, USER_TYPE, WORKSPACE_TYPE } from "@constant";
import { LINK } from "@link";

import { getOnePermissionID } from "@services/User";
import { updateWorkspace } from "@services/Workspace";
import { requestForgetPassword, updateInfoUser } from "@services/Auth";
import PieChartUsageAccount from "@app/pages/User/PieChartUsageAccount";
import RenameDescription from "@app/component/RenameDescription";

import ArrowUpRight from "@component/SvgIcons/ArrowUpRight";
import EditPen from "@src/app/component/SvgIcons/Edit/EditPen";

import * as workspaceRedux from "@src/ducks/workspace.duck";
import * as auth from "@src/ducks/auth.duck";

import "./User.scss";
import ModalChangePassword from "./ModalChangePassword";

const User = ({ availableWorkspaces, user, ...props }) => {
  usePageViewTracker("User");
  const { t } = useTranslation();

  const [limitPermissionUser, setLimitPermissionUser] = useState([]);
  const [isShowModalRename, setIsShowModalRename] = useState(false);
  const [isShowChangePassword, setShowChangePassword] = useState(false);

  const linkUpgrade = user?.type === USER_TYPE.STUDENT ? LINK.PRICING : LINK.SUBSCRIPTION;

  const myWorkspace = useMemo(() => {
    return availableWorkspaces?.find((workspace) => workspace?.type === WORKSPACE_TYPE.PERSONAL);
  }, [availableWorkspaces]);
  useEffect(() => {
    getPermissionUser();
  }, []);

  const [subscriptionName, isFree] = useMemo(() => {
    return [
      user?.subscription?.packageId?.name,
      user?.subscription?.packageId?.order === 1,
    ];
  }, [user]);

  const onChangePassword = async () => {
    if (user?.hasPassword) {
      onToggleChangePassword();
      return;
    }
    const apiResponse = await requestForgetPassword({ email: user.email });
    if (apiResponse) {
      toast.success(t("AUTH_MESSAGE_FORGOT_PASSWORD_SUCCESS").format(t('YOUR_EMAIL_ADDRESS')), { unique: true });
    }
  };

  const onSubmitDescription = async (description) => {
    const dataRequest = { _id: myWorkspace?._id, description };
    const dataResponse = await updateWorkspace(dataRequest, true);
    if (dataResponse) {
      const newAvailableWorkspaces = availableWorkspaces.map((workspace) => {
        if (workspace._id === dataResponse._id) return dataResponse;
        return workspace;
      });
      props.setAvailableWorkspaces(newAvailableWorkspaces);
      return true;
    } else return false;
  };

  const handleExpireDate = (date) => {
    const today = moment();
    const expirationDate = moment(date);
    return expirationDate.isBefore(today.subtract(3, "days"));
  };

  const submitNameUser = async (newUserName) => {
    const apiResponse = await updateInfoUser({ fullName: newUserName });
    if (apiResponse) {
      props.requestUser();
      toast.success("UPDATE_NAME_SUCCESS");
      setIsShowModalRename(false);
    }
  };

  const getPermissionUser = async () => {
    const apiResponse = await getOnePermissionID(user?._id);
    if (apiResponse) {
      setLimitPermissionUser(apiResponse);
    }
  };

  const onToggleChangePassword = () => {
    setShowChangePassword(pre => !pre);
  };

  return <div id="account">
    <div className="account__content">
      <div className="account__content__item account__content__item__name">
        <div className="account__content__item__name__left">
          <div className="item__label">{t("NAME")}</div>
          <div className="item__value">
            {user?.fullName}
          </div>
        </div>
        <div className="edit-icon" onClick={() => setIsShowModalRename(true)}>
          <EditPen />
        </div>
      </div>
      <div className="account__content__item">
        <div className="item__label">{t("EMAIL")}</div>
        <div className="item__value">{user?.email}</div>
      </div>
      <div className="account__content__item">
        <div className="item__label">{t("PASSWORD")}</div>
        <div className="item__value password">
          {user?.hasPassword && <div className="asterisks-password">***********</div>}
          <div className="link-change-password" onClick={onChangePassword}>
            {t(user?.hasPassword ? "CHANGE_PASSWORD" : "SET_PASSWORD")}
          </div>
        </div>
      </div>
      {user.type !== USER_TYPE.STUDENT && <div className="account__content__item">
        <div className="item__label">{t("WORKSPACE")}</div>
        <div className="item__value item__value-workspace">
          <div className="workspace-name">{`${user?.fullName}'s ${t("WORKSPACE")}`}</div>
          <DescriptionWorkspace
            value={myWorkspace?.description}
            onSubmit={onSubmitDescription}
          />
        </div>
      </div>}
      {user.type !== USER_TYPE.STUDENT && <>
        <div className="account__content__item">
          <div className="item__label">{t("SUBSCRIPTION")}</div>
          <div className="item__value item__value-subscription">
            <div className="subscription-package-name">
              <span>{subscriptionName}</span>
            </div>
            {!isFree && <div className="subscription-package-term">
              <span>{formatDate(user.subscription?.startDate)}</span>
              <span className="ellipse" />
              <span className="subscription-package-term-end">
                {formatDate(user.subscription?.endDate)}
              </span>
            </div>}
          </div>
        </div>
        <div className="account__content__item">
          <div className="item__label">{t("PAYMENT_HISTORY")}</div>
          <div className="item__value">
            <Link className="link-payment-history" to={LINK.PAYMENT_HISTORY}>
              <span>{t("VIEW_DETAIL")}</span>
            </Link>
          </div>
        </div>
      </>}
    </div>
    {user.type !== USER_TYPE.STUDENT && <>
      <div className="chart-remaning-container">
        <div className="chart-remaning-item">
          <div className="chart-remaning-info">
            <div className="chart-remaning-info__left">
              <div className="chart-remaning-info__title">{t("TEXT_TOOL")}</div>
              <div className="chart-remaning-info__description">
                {isNaN(limitPermissionUser?.accessLimit?.textLimit) ? t("UNLIMITED") : `${limitPermissionUser?.accessLimit?.textLimit} ${t("RUNS")}/${t("MONTH")}`}
              </div>
            </div>
            {user?.subscription?.endDate && (
              <div className="chart-remaning-info__right">
                <div className="chart-remaning-info__title">{t("LIMIT_RESET_ON")}</div>
                {handleExpireDate(user?.subscription?.endDate) ? (
                  <div className="chart-remaning-info__exp">
                    <span
                      className="chart-remaning-info__expTitle">{formatDate(user?.subscription?.endDate)}. &nbsp;</span>
                    {/*<Link to={LINK.PACKAGE} className="chart-remaning-info__exp-link">*/}
                    {/*  {t("BUY_MORE")}*/}
                    {/*</Link>*/}
                  </div>
                ) : <div
                  className="chart-remaning-info__description">{formatDate(user?.subscription?.endDate)}</div>}

              </div>)}
          </div>

          <PieChartUsageAccount total={limitPermissionUser?.accessLimit?.textLimit}
            usage={limitPermissionUser?.accessLimit?.textUsed} />
          <span
            className="chart-remaning-info__avalibled">{limitPermissionUser?.accessLimit?.textUsed} {t("RUNS")}/ {isNaN(limitPermissionUser?.accessLimit?.textLimit) ? t("UNLIMITED") : `${limitPermissionUser?.accessLimit?.textLimit} ${t("RUNS")}`}</span>
        </div>
        <div className="chart-remaning-item">
          <div className="chart-remaning-info">
            <div className="chart-remaning-info__left">
              <div className="chart-remaning-info__title">{t("MEDIA_TOOL")}</div>
              <div className="chart-remaning-info__description">
                {isNaN(limitPermissionUser?.accessLimit?.mediaLimit) ? t("UNLIMITED") : `${limitPermissionUser?.accessLimit?.mediaLimit} ${t("RUNS")}/${t("MONTH")}`}
              </div>
            </div>
            {user?.subscription?.endDate && (
              <div className="chart-remaning-info__right">
                <div className="chart-remaning-info__title">{t("LIMIT_RESET_ON")}</div>
                {handleExpireDate(user?.subscription?.endDate) ? (
                  <div className="chart-remaning-info__exp">
                    <span
                      className="chart-remaning-info__expTitle">{formatDate(user?.subscription?.endDate)}</span>
                    {/*<Link to={LINK.PACKAGE} className="chart-remaning-info__exp-link">*/}
                    {/*  {t("BUY_MORE")}*/}
                    {/*</Link>*/}
                  </div>
                ) : <div
                  className="chart-remaning-info__description">{formatDate(user?.subscription?.endDate)}</div>}
              </div>)}
          </div>
          <PieChartUsageAccount total={limitPermissionUser?.accessLimit?.mediaLimit}
            usage={limitPermissionUser?.accessLimit?.mediaUsed} />
          <span
            className="chart-remaning-info__avalibled">{limitPermissionUser?.accessLimit?.mediaUsed} {t("RUNS")}/ {isNaN(limitPermissionUser?.accessLimit?.mediaLimit) ? t("UNLIMITED") : `${limitPermissionUser?.accessLimit?.mediaLimit} ${t("RUNS")}`}</span>
        </div>
      </div>
    <Link className="btn-upgrade" to={linkUpgrade}>
        <AntButton type={BUTTON.DEEP_NAVY} icon={<ArrowUpRight />}>{t("UPGRADE_NOW")}</AntButton>
      </Link>
    </>}
    <RenameDescription
      isShowModal={isShowModalRename}
      initialValue={user?.fullName}
      title={t('EDIT_NAME')}
      handleClose={() => setIsShowModalRename(!isShowModalRename)}
      handleSubmit={submitNameUser}
      required
    />
    <ModalChangePassword open={isShowChangePassword} onCancel={onToggleChangePassword} />

  </div>;
};

function mapStateToProps(store) {
  const { user } = store.auth;
  const { availableWorkspaces } = store.workspace;
  return { availableWorkspaces, user };
}

const mapDispatchToProps = {
  ...workspaceRedux.actions,
  ...auth.actions,
};

export default connect(mapStateToProps, mapDispatchToProps)(User);

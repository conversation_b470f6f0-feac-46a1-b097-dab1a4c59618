import { useEffect, useMemo } from "react";
import { Layout } from "antd";
import { Link, matchPath, useLocation } from "react-router-dom";
import { connect } from "react-redux";

import MainAside from "@app/layout/Aside/MainAside";
import AdminAside from "@app/layout/Aside/AdminAside";

import { LINK } from "@link";

import CLICKEE_LOGO from "@src/asset/logo/clickee-logo.svg";

import * as workspaceRedux from "@src/ducks/workspace.duck";

import "./Aside.scss";

function Aside({ availableWorkspaces, user, ...props }) {
  const { pathname } = useLocation();

  const isShowAdminAside = useMemo(() => user?.isSystemAdmin && pathname.includes(LINK.ADMIN_PAGE), [pathname]);
  
  useEffect(() => {
    if (!availableWorkspaces) {
      props.getAvailableWorkspaces();
    }
  }, []);
  
  if ([LINK.SUBSCRIPTION, LINK.PAYMENT_ID.format(":id"), LINK.PAYMENT_VNPAY].some((item) => matchPath(item, pathname))) return null;
  
  return <Layout.Sider id="aside" width={264}>
    <div className="aside-header">
      <Link to={LINK.HOMEPAGE}>
        <img className="aside-header__logo" src={CLICKEE_LOGO} alt="" />
      </Link>
    </div>
    
    {isShowAdminAside ? <AdminAside /> : <MainAside />}
  
  </Layout.Sider>;
}

function mapStateToProps(store) {
  const { availableWorkspaces } = store.workspace;
  const { user } = store.auth;
  return { availableWorkspaces, user };
}

const mapDispatchToProps = {
  ...workspaceRedux.actions,
};

export default (connect(mapStateToProps, mapDispatchToProps)(Aside));

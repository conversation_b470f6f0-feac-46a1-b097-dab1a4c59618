import { useEffect, useMemo, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";

import NoData from "@component/NoData";
import AntButton from "@component/AntButton";
import SearchInput from "@component/SearchInput";
import Loading from "@component/Loading";
import ToolGroup from "./ToolGroup";

import { BUTTON, CONSTANT, TOOL_GROUP_ICON } from "@constant";

import { cloneObj, formatUnique } from "@common/functionCommons";

import * as tool from "@src/ducks/tool.duck";

import "./Tool.scss";

const Tool = ({ projectId, user, toolData, listToolAvailable, toolType, addContentBlock, ...props }) => {
  const { t } = useTranslation();
  const [searchValue, setSearchValue] = useState("");
  const [categoryActive, setCategoryActive] = useState(null);
  const [toolLoading, setToolLoading] = useState(null);
  const [isLoadingAccessTool, setLoadingAccessTool] = useState(false);
  
  
  useEffect(() => {
    if (!toolData) {
      setToolLoading(true);
      props.getToolAvailable();
    } else {
      setToolLoading(false);
    }
  }, [toolData]);
  
  const toolDataObj = useMemo(() => {
    if (!toolData) return {};
    
    return Object.keys(toolData).reduce((grouped, element) => {
      grouped[element] = cloneObj(toolData[element]);
      grouped[element].toolList = grouped[element].toolList.filter(tool => tool.name?.toLowerCase().includes(searchValue?.toLowerCase()));
      
      if (toolType) {
        grouped[element].toolList = grouped[element].toolList.filter(tool => toolType === tool.type);
      }
      
      return grouped;
    }, {});
  }, [toolData, searchValue]);
  
  async function handleAccessTool(...args) {
    addContentBlock(...args);
  }
  
  const onChangeSearch = (e) => {
    setSearchValue(e.target.value);
  };
  
  const onClearSearch = () => {
    setSearchValue("");
  };
  
  const totalTool = useMemo(() => {
    if (!toolDataObj) return 0;
    const toolArr = Object.entries(toolDataObj)
                          .filter(([key]) => key !== CONSTANT.FAVORITE)
                          .map(([key, value]) => value.toolList).flat()
                          .map(tool => tool._id);
    return formatUnique(toolArr).length;
  }, [toolDataObj]);
  
  // group active tools by groupToolIds
  const toolGrouped = useMemo(() => {
    let activeTools = [];
    if (categoryActive) {
      activeTools = toolDataObj[categoryActive]?.toolList;
    } else {
      activeTools = listToolAvailable?.filter(tool => tool.name?.toLowerCase().includes(searchValue?.toLowerCase())) || [];
    }
    
    if (toolType) {
      activeTools = activeTools?.filter(tool => toolType === tool.type);
    }
    
    return cloneObj(activeTools).reduce(function (grouped, element) {
      element.groupToolIds.forEach(group => {
        grouped[group.code] ||= group;
        grouped[group.code].icon ||= TOOL_GROUP_ICON[group.code];
        grouped[group.code].tools ||= [];
        grouped[group.code].tools.push(element);
      });
      return grouped;
    }, {});
  }, [toolDataObj, categoryActive]);
  
  if (toolLoading) {
    return <Loading active transparent />;
  }
  return <Loading active={isLoadingAccessTool} fixedMiddleScreen id="tools">
    <div className="search-wrapper">
      <SearchInput
        size="large"
        className="col-span-1"
        value={searchValue}
        placeholder={t("SEARCH_FOR_TOOL")}
        onChange={onChangeSearch}
        onClear={onClearSearch}
      />
    </div>
    
    <div className="tools__group-buttons">
      <AntButton
        size="small"
        type={categoryActive ? BUTTON.WHITE : BUTTON.DEEP_NAVY}
        onClick={() => setCategoryActive(null)}
      >
        {t("ALL") + " (" + totalTool + ")"}
      </AntButton>
      
      {Object.entries(toolDataObj)?.map(([key, value], index) => {
        let title;
        const length = " (" + value.toolList?.length + ")";
        if (key === "ORGANIZATION") {
          title = `${user.organizationId?.name}` + length;
        } else {
          title = t(value.lang) + length;
        }
        return <AntButton
          size="small"
          key={index}
          type={categoryActive === key ? value.activeColor : value.color}
          onClick={() => setCategoryActive(key)}
        >
          {title}
        </AntButton>;
      })}
    </div>
    {!Object.values(toolGrouped).length && <NoData searchNoData={true}>{t("NO_DATA")}</NoData>}
    {Object.values(toolGrouped).map((group, index) => {
      return <ToolGroup
        key={index}
        toolGroupData={group}
        addContentBlock={handleAccessTool}
        {...props}
      />;
    })}
  </Loading>;
};

function mapStateToProps(store) {
  const { user } = store.auth;
  const { toolData, listToolAvailable } = store.tool;
  return { user, toolData, listToolAvailable };
}

const mapDispatchToProps = {
  ...tool.actions,
};

export default connect(mapStateToProps, mapDispatchToProps)(Tool);


import React from 'react';
import { List, Card, Typography, Tag, Spin, Empty, Space, Tooltip } from 'antd';
import { CheckCircleOutlined, ClockCircleOutlined, EyeOutlined, BarChartOutlined, LineChartOutlined } from '@ant-design/icons';
import AntButton from "@component/AntButton";
import { BUTTON } from '@constant';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import { useNavigate } from 'react-router-dom';
import {LINK} from '@src/constants/link';

const { Title, Text, Paragraph } = Typography;

const CompletedSessionsListComponent = ({ sessions, loading, courseId }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleViewDetails = (sessionId) => {
    console.log("courseId", courseId);
    console.log("sessionId", sessionId);
    if (courseId && sessionId) {
      navigate(LINK.ROLE_PLAY_SESSION_RESULT.format(courseId, sessionId));
    }
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!sessions || sessions.length === 0) {
    return (
      <Card title={<Title level={5}>{t('COMPLETED_SESSIONS_HISTORY', 'Lịch sử luyện tập')}</Title>}>
        <Empty description={t('NO_COMPLETED_SESSIONS', 'Chưa có buổi luyện tập nào được hoàn thành cho khóa học này.')} />
      </Card>
    );
  }

  const getStatusTag = (session) => {
    if (session.status === "analyzed") {
      return (
        <Tag icon={<BarChartOutlined />} color="cyan">
          {t('ANALYZED', 'Đã phân tích')}
        </Tag>
      );
    } else if (session.isCompleted) {
      return (
        <Tag icon={<CheckCircleOutlined />} color="success">
          {t('COMPLETED', 'Hoàn thành')}
        </Tag>
      );
    } else {
      return (
        <Tag icon={<ClockCircleOutlined />} color="processing">
          {session.status || t('IN_PROGRESS', 'Đang thực hiện')}
        </Tag>
      );
    }
  };

  return (
    <Card className="role-play-session-screen__session-list" title={<Title level={5}>{t('COMPLETED_SESSIONS_HISTORY', 'Lịch sử luyện tập')}</Title>}>
      <List
        itemLayout="vertical"
        dataSource={sessions}
        pagination={{
          pageSize: 3, // Show 5 items per page
          align: 'center',
        }}
        renderItem={(session) => (
          <List.Item
            key={session._id}
            actions={[
              <AntButton
                type={BUTTON.TEXT}
                icon={<EyeOutlined />}
                onClick={() => handleViewDetails(session._id)}
              >
                {t('VIEW_DETAILS', 'Xem chi tiết')}
              </AntButton>
            ]}
          >
            <List.Item.Meta
              title={session.courseId?.name || t('UNKNOWN_COURSE', 'Khóa học không xác định')}
              description={
                <Space direction="vertical" size="small">
                  <Text type="secondary">
                    {t('COMPLETED_ON', 'Hoàn thành vào:')}{' '}
                    {session.endTime ? moment(session.endTime).format('DD/MM/YYYY HH:mm') : t('N/A', 'N/A')}
                  </Text>
                  <div>
                    {getStatusTag(session)}
                    {session.analysisId?.result?.simulationScore !== undefined && (
                      <Tag icon={<LineChartOutlined />} color="blue" style={{ marginLeft: '8px' }}>
                        {t('SCORE', 'Điểm:')} {session.analysisId.result.simulationScore}
                      </Tag>
                    )}
                  </div>
                  {session.duration && (
                    <Text>
                      {t('DURATION', 'Thời lượng:')} {moment.duration(session.duration, 'seconds').humanize()}
                    </Text>
                  )}
                  {session.analysisId?.result?.topInsights && session.analysisId.result.topInsights.length > 0 && (
                    <div style={{ marginTop: '8px' }}>
                      <Text strong>{t('TOP_INSIGHTS', 'Nhận xét chính:')}</Text>
                      <ul style={{ marginTop: '4px', paddingLeft: '20px' }}>
                        {session.analysisId.result.topInsights.map((insight, idx) => (
                          <li key={idx}>
                            <Text>{insight}</Text>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </Space>
              }
            />
          </List.Item>
        )}
      />
    </Card>
  );
};

export default CompletedSessionsListComponent;

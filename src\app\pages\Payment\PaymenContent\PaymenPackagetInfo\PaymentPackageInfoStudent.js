import { useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";
import dayjs from "dayjs";

import { convertDateToText, renderTextTime } from "@common/functionCommons";
import { getAllFeature } from "@services/Package";

import { CONSTANT, LANGUAGE } from "@constant";
import TICKET_ICON from "@src/asset/icon/ticket/ticket-navy.svg";
import { getCurrentPackage } from "@src/app/services/Subscription";
import { useParams } from "react-router-dom";

function PaymentPackageInfoStudent({ user, ...props }) {
  const { packageData, unitPrice, intervalCount, unitName } = props;
  const { t, i18n } = useTranslation();
  const { id } = useParams();

  const isAddon = packageData?.type === CONSTANT.ADDON;
  const packageDuration = isAddon ? '' : renderTextTime(intervalCount, t(unitName?.toUpperCase()), i18n.language);

  const [packageDateInfo, setPackageDateInfo] = useState({
    startDate: dayjs(),
    endDate: dayjs().add(intervalCount || 1, unitPrice?.toLowerCase() || unitName)
  });
  const [features, setFeatures] = useState([]);

  useEffect(() => {
    requestData();
  }, []);

  const requestData = async () => {
    const [subscriptionsResponse, featureResponse] = await Promise.all([getCurrentPackage(), getAllFeature()]);
    if (featureResponse) setFeatures(featureResponse);
    if (subscriptionsResponse) {
      const oldSubscription = subscriptionsResponse?.find((subscription) => subscription?.packageId?._id === id);
      if (oldSubscription) {
        const newStartDate = dayjs(oldSubscription?.endDate).add(1, 'day');
        setPackageDateInfo({
          startDate: newStartDate,
          endDate: newStartDate.add(intervalCount || 1, unitPrice?.toLowerCase() || unitName)
        });
      }
    }
  }

  function renderDescription() {
    if (isAddon) {
      const quantity = Object.values(packageData?.features)?.[0];
      return `${t("PAYMENT_ADD_ON_PACKAGE_DESCRIPTION_STUDENT").format(quantity)}${quantity > 1 && i18n.language === LANGUAGE.EN ? 's' : ''}`;
    }
    return (<>
      {t('SERVICE_PACKAGE_START_FROM')}
      <span className="payment-package-info__date">{convertDateToText(packageDateInfo?.startDate)}</span>
      {t('TO_DATE')}
      <span className="payment-package-info__date">{convertDateToText(packageDateInfo?.endDate)}</span>
      {` (${packageDateInfo.endDate.diff(packageDateInfo.startDate, 'day')} ${t("USING_DATE")})`}
    </>)
  }

  return <div className="payment-package-item">
    <div className="payment-package-info">
      <div className="payment-package-item__label">
        <img src={TICKET_ICON} alt="" />
        {t('PAYNMENT_SERVICE_PACKAGE')}
      </div>
      <div className="payment-package-info__name">
        {packageData?.name}
        {!!packageDuration && <span className="payment-package-info__package-duaration">{packageDuration}</span>}
      </div>
      <div className="payment-package-info__description">{renderDescription()}</div>
    </div>
  </div>
}

const mapStateToProps = (state) => {
  const { user } = state.auth;
  return { user };
};

const connector = connect(mapStateToProps, {});

export default connector(PaymentPackageInfoStudent);
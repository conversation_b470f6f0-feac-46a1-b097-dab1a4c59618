import { useState } from "react";
import { Radio, Space } from 'antd';
import { useTranslation } from "react-i18next";

import WALLET_ICON from "@src/asset/icon/wallet/wallet-navy.svg";
import VN_PAY_ICON from "@src/asset/icon/pay/vn-pay.svg";
// import ZALO_PAY_ICON from "@src/asset/icon/pay/zalo-pay.svg";
import ZALO_PAY_DISABLE_ICON from "@src/asset/icon/pay/zalo-pay-disable.svg";
// import PAYPAL_ICON from "@src/asset/icon/pay/paypal.svg";
import PAYPAL_DISABLE_ICON from "@src/asset/icon/pay/paypal-disable.svg";

export default function FormOfPayment() {
  const { t } = useTranslation();

  const [activeKey, setActiveKey] = useState(1);

  const onChangeMethod = (e) => {
    setActiveKey(e.target.value);
  };

  return (<div className="payment-package-item">
    <div className="payment-package-method">
      <div className="payment-package-item__label">
        <img src={WALLET_ICON} alt="" />
        {t('FORM_OF_PAYMENT')}
      </div>
      <Radio.Group onChange={onChangeMethod} defaultValue={activeKey}>
        <Space direction="vertical" classNames="payment-package-method__options">
          <Radio value={1}>
            <div className="payment-package-item__label">
              <img src={VN_PAY_ICON} alt='' />
              {t('PAYMENT_VIA_VNPAY')}
            </div>
          </Radio>
          <Radio value={2} disabled>
            <div className="payment-package-item__label">
              <img src={PAYPAL_DISABLE_ICON} alt='' />
              {t('PAYMENT_VIA_PAYPAL')}
            </div>
          </Radio>
          <Radio value={3} disabled>
            <div className="payment-package-item__label">
              <img src={ZALO_PAY_DISABLE_ICON} alt='' />
              {t('PAYMENT_VIA_ZALOPAY')}
            </div>
          </Radio>
        </Space>
      </Radio.Group>

    </div>
  </div>)
}
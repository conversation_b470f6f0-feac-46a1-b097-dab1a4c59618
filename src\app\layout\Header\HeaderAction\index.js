import { useEffect, useMemo } from "react";
import { Button, Dropdown } from "antd";
import { connect } from "react-redux";
import { Link, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

import ToggleTheme from "@app/layout/Header/HeaderAction/ToggleTheme";

import { LINK } from "@link";
import { CONSTANT, LANGUAGE } from "@constant";

import Setting from "@component/SvgIcons/Setting";
import Logout from "@component/SvgIcons/Logout";
import AntButton from "@component/AntButton";

import * as app from "@src/ducks/app.duck";
import * as auth from "@src/ducks/auth.duck";
import * as tool from "@src/ducks/tool.duck";

import VN_FLAG from "@src/asset/icon/flag/vn.svg";
import UK_FLAG from "@src/asset/icon/flag/uk.svg";

import "./HeaderAction.scss";

function HeaderAction({ user, ...props }) {
  const { t, i18n } = useTranslation();
  const location = useLocation();

  const handleChangeLang = lang => {
    if (i18n.language !== lang) {
      props.setLanguage(lang);
      i18n.changeLanguage(lang);
      props.localizeTool();
    }
  };

  const items = useMemo(() => {
    let menus = [];
    if (user.isSystemAdmin) menus.push({
      key: LINK.TOOL_STUDIO,
      label: <Link to={LINK.ADMIN.SETTING}>{t("ADMIN_MANAGER")}</Link>,
      icon: <Setting />,
    });
    if (user?.role === CONSTANT.ADMIN) menus.push({
      key: LINK.ORGANIZATION,
      label: <Link to={LINK.ORGANIZATION}>{t("ORGANIZATION_SETTING")}</Link>,
      icon: <Setting />,
    });

    return [...menus,
      {
        key: LINK.ACCOUNT,
        label: <Link to={LINK.ACCOUNT}>{t("ACCOUNT_SETTINGS")}</Link>,
        icon: <Setting />,
      },
      {
        key: LINK.LOGOUT,
        label: t("LOGOUT"),
        icon: <Logout />,
        onClick: props.logout,
      },
    ];
  }, [t, user]);

  return <div className="header-action">
    <ToggleTheme />

    <Dropdown
      menu={{
        className: "language-menu",
        selectedKeys: [i18n.language],
        items: [
          { key: LANGUAGE.EN, label: "ENG", icon: <img src={UK_FLAG} alt="UK" />, onClick: () => handleChangeLang(LANGUAGE.EN) },
          { key: LANGUAGE.VI, label: "VIE", icon: <img src={VN_FLAG} alt="VN" />, onClick: () => handleChangeLang(LANGUAGE.VI) },
        ],
      }}
      placement="bottomRight"
      className="ant-btn-white language-dropdown"
      trigger="click"
    >
      <img src={i18n.language === LANGUAGE.EN ? UK_FLAG : VN_FLAG} alt="" style={{ cursor: 'pointer' }} />
    </Dropdown>

    <Dropdown
      menu={{
        selectedKeys: [location.pathname],
        items,
      }}
      placement="bottomRight"
      className="ant-btn-white"
      trigger="click"
    >
      <AntButton size="large">
        <span className="account-button-text">
          {user.fullName}
        </span>
      </AntButton>
    </Dropdown>
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

const mapDispatchToProps = {
  ...app.actions, ...auth.actions,
  ...tool.actions,
};
export default connect(mapStateToProps, mapDispatchToProps)(HeaderAction);

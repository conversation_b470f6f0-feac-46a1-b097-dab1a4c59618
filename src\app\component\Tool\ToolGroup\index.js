import { useCallback, useMemo, useState } from "react";
import { matchPath, useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { createProjectFromTool } from "@services/Project";

import { LINK } from "@link";

import ToolGrid from "../ToolGrid";

import "./ToolGroup.scss";
import YoutubeModal from "@component/YoutubeModal";

const ToolGroup = ({ ...props }) => {
  const { toolGroupData, hanldeAfterFavoriteTool } = props;
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const { i18n } = useTranslation();
  
  const [toolLoading, setToolLoading] = useState(null);
  
  const [statePreview, setStatePreview] = useState({
    isShowModal: false,
    linkYoutube: null,
  });
  
  const isProject = useMemo(() => {
    return !!matchPath(LINK.PROJECT_DETAIL.format(":id"), pathname);
  }, [pathname]);
  
  const handleAccessTool = useCallback(async (toolId) => {
    setToolLoading(toolId);
    if (isProject) {
      await props.addContentBlock({ toolId });
    } else {
      const projectResponse = await createProjectFromTool({ toolId });
      if (projectResponse) {
        navigate(LINK.PROJECT_DETAIL.format(projectResponse._id));
      }
    }
    
    setToolLoading(null);
  }, [isProject]);
  
  function handleShowPreview(isShowModal, linkYoutube = null) {
    if (isShowModal) {
      setStatePreview({ isShowModal, linkYoutube });
    } else {
      setStatePreview(prevState => Object.assign({}, prevState, { isShowModal }));
    }
  }
  
  return <>
    <div className="tool-group">
      <div className="tool-group__header">
        <div className="tool-group__icon">
          <img src={toolGroupData.icon} alt="" />
        </div>
        <div className="tool-group__info">
          <div className="tool-group__name">{toolGroupData?.groupName}</div>
          <div className="tool-group__description">{toolGroupData?.description}</div>
        </div>
      </div>
      <div className="tool-group__tool-list">
        {toolGroupData.tools?.map((tool, index) => {
          return <ToolGrid
            key={index}
            toolItemData={tool}
            toolLoading={toolLoading}
            handleAccessTool={handleAccessTool}
            onShowPreview={() => handleShowPreview(true, tool.linkYoutube)}
            hanldeAfterFavoriteTool={hanldeAfterFavoriteTool}
          />;
        })}
      </div>
    </div>
    
    <YoutubeModal
      isOpen={statePreview.isShowModal}
      linkYoutube={statePreview.linkYoutube}
      handleCancel={() => handleShowPreview(false)}
    />
  </>;
};

export default ToolGroup;
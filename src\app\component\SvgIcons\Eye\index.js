import React from "react";

export default function Eye() {
  return <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Medium/eye" clipPath="url(#clip0_1582_37651)">
      <g id="Icon">
        <path d="M0.666626 8.49935C0.666626 8.49935 3.33329 3.16602 7.99996 3.16602C12.6666 3.16602 15.3333 8.49935 15.3333 8.49935C15.3333 8.49935 12.6666 13.8327 7.99996 13.8327C3.33329 13.8327 0.666626 8.49935 0.666626 8.49935Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
        <path d="M7.99996 10.4993C9.10453 10.4993 9.99996 9.60392 9.99996 8.49935C9.99996 7.39478 9.10453 6.49935 7.99996 6.49935C6.89539 6.49935 5.99996 7.39478 5.99996 8.49935C5.99996 9.60392 6.89539 10.4993 7.99996 10.4993Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      </g>
    </g>
    <defs>
      <clipPath id="clip0_1582_37651">
        <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
      </clipPath>
    </defs>
  </svg>
  
}

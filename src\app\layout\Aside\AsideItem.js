import { useMemo } from "react";
import { Link, useLocation } from "react-router-dom";
import clsx from "clsx";
import { LINK } from "@link";


function AsideItem({ title, linkTo, img, imgActive, state, ...props }) {
  const location = useLocation();
  const isActive = useMemo(() => {
    const asideType = state?.asideType;
    if (location.pathname.includes(LINK.OPENAI_COST)) {
      return (linkTo.includes(LINK.OPENAI_COST))
    }
    if (location.pathname.includes(LINK.USER_TRACKING)) {
      return (linkTo.includes(LINK.USER_TRACKING))
    }
    return ((location.pathname === linkTo && (!asideType || asideType === location?.state?.asideType))
      || (location.pathname !== linkTo && location.pathname.includes(linkTo)));
  }, [location, state]);

  return <Link
    to={linkTo}
    className={clsx("aside-item", { "aside-item__active": isActive })}
    title={title}
    {...state ? { state } : {}}
  >
    <img src={isActive ? imgActive : img} alt="" className="aside-item__icon" />
    <div className="aside-item__title">
      {title}
    </div>
  </Link>;
}

export default AsideItem;
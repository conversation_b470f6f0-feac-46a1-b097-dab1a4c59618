import { useMemo } from "react";
import { useTranslation } from "react-i18next";

import { DISCOUNT_TYPE } from "@constant";
import { renderMoney } from "@src/common/functionCommons";

export default function PaymentCalculate({ ...props }) {
  const { priceData, applyDiscounts, featureValue, setIsFreePayment } = props;
  const { t } = useTranslation();
  const price = priceData?.promotionalPrice || priceData?.unitAmount;

  const { totalBeforeDiscount, totalAfterDiscount, discountValues } = useMemo(() => {
    if (!priceData) return ({ totalBeforeDiscount: 0, totalAfterDiscount: 0, discountValues: [] });

    const totalBeforeDiscount = priceData?.quantity ? (priceData?.quantity * price / featureValue) : price;
    let money = totalBeforeDiscount;
    const discounts = applyDiscounts.map(discountData => {
      let discountValue = discountData.discount;
      if (discountData.type === DISCOUNT_TYPE.PERCENTAGE) {
        discountValue = (totalBeforeDiscount * discountData.discount) / 100;
      }
      money = money - discountValue;
      if (totalBeforeDiscount - discountValue < 5000) {
        setIsFreePayment(true);
        discountValue = totalBeforeDiscount;
        money = 0;
      }
      return { code: discountData?.code, discount: discountValue }
    });
    return {
      totalBeforeDiscount: totalBeforeDiscount,
      totalAfterDiscount: money,
      discountValues: discounts
    }
  }, [priceData, applyDiscounts]);

  return <div className="payment-package-item">
    <div className="payment-package-calculate">
      <div className="payment-package-calculate__data">
        <div className="payment-package-item__label">{t('PAYMENT')}</div>
        {renderMoney(Math.ceil(totalBeforeDiscount))}
      </div>
      <div className="payment-package-calculate__data">
        <div className="payment-package-item__label">{t('DISCOUNT')}</div>
        {renderMoney(Math.ceil(totalBeforeDiscount) - Math.ceil(totalAfterDiscount))}
      </div>
      {discountValues.map(discountValue => {
        return <div className="payment-package-calculate__data payment-package-calculate__discount" key={discountValue.code}>
          <div className="payment-package-item__label">{discountValue.code}</div>
          {renderMoney(Math.floor(discountValue.discount))}
        </div>
      })}
      <div className="payment-package-calculate__data payment-package-calculate__total">
        <div className="payment-package-item__label">{t('TOTAL_AMOUNT')}</div>
        {renderMoney(Math.ceil(totalAfterDiscount))}
      </div>
    </div>
  </div>
}
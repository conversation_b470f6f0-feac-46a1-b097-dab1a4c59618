import { Navigate, Route, Routes } from "react-router-dom";
import { connect } from "react-redux";

import { LINK } from "@link";
import { CONSTANT, USER_TYPE } from "@constant";

import NeedAccess from "../component/NeedAccess";

import Settings from "@src/app/pages/AdminPage/Settings";

// Import màn hình quản lý khoá học RolePlay
import CoursesManagementScreen from "@app/pages/RolePlay/Admin/CoursesManagementScreen";
// Import màn hình quản lý AI Persona
import AIPersonaManagementScreen from "@app/pages/RolePlay/Admin/AIPersonaManagementScreen";
// Import màn hình chi tiết AI Persona
import AIPersonaDetailScreen from "@app/pages/RolePlay/Admin/AIPersonaDetailScreen";
// Import màn hình chi tiết Course
import CourseDetailScreen from "@app/pages/RolePlay/Admin/CourseDetailScreen";
// Import màn hình quản lý RolePlayInstruction
import RolePlayInstructionManagementScreen from "@app/pages/RolePlay/Admin/RolePlayInstructionManagementScreen";
// Import màn hình chi tiết RolePlayInstruction
import RolePlayInstructionDetailScreen from "@app/pages/RolePlay/Admin/RolePlayInstructionDetailScreen";



const AdminPageRouter = ({ user }) => {

  function linkToAdmin(adminUrl) {
    return adminUrl.replace(LINK.ADMIN_PAGE, "");
  }

  // Allow access for teachers and system admins
  if (!user?.isSystemAdmin && user?.type !== USER_TYPE.TEACHER) return <NeedAccess />;

  return (
    <Routes>
      <Route>
        {/* Settings Route */}
        <Route path={linkToAdmin(LINK.ADMIN.SETTING)} element={<Settings />} />

        {/* RolePlay Routes */}
        <Route path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT)} element={<CoursesManagementScreen />} />
        <Route path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT)} element={<AIPersonaManagementScreen />} />
        <Route path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_CREATE)} element={<AIPersonaDetailScreen />} />
        <Route path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_DETAIL.format(":id"))} element={<AIPersonaDetailScreen />} />
        <Route path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_COURSE_CREATE)} element={<CourseDetailScreen />} />
        <Route path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_COURSE_DETAIL.format(":id"))} element={<CourseDetailScreen />} />

        {/* RolePlayInstruction Routes */}
        <Route path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT)} element={<RolePlayInstructionManagementScreen />} />
        <Route path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_INSTRUCTION_CREATE)} element={<RolePlayInstructionDetailScreen />} />
        <Route path={linkToAdmin(LINK.ADMIN.ROLE_PLAY_INSTRUCTION_DETAIL.format(":id"))} element={<RolePlayInstructionDetailScreen />} />

        <Route path="*" element={<Navigate to={LINK.ERROR_404} replace />} />
      </Route>
    </Routes>
  );
};

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(AdminPageRouter);

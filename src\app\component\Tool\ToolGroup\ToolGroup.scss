.tool-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  width: 100%;

  .tool-group__header {
    display: flex;
    flex-direction: row;
    gap: 8px;

    .tool-group__icon {
      display: flex;
      align-items: baseline;
    }

    .tool-group__info {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .tool-group__name {
        font-size: 20px;
        font-weight: 600;
      }

      .tool-group__description {
        font-size: 16px;
        font-weight: 400;
      }
    }
  }

  .tool-group__tool-list {
    display: grid;
    gap: 24px;
    width: 100%;

    @media screen and (min-width: 1700px) {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }

    @media screen and (min-width: 1536px) and (max-width: 1699.98px) {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }

    @media screen and (min-width: 1250px) and (max-width: 1535.98px) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    @media screen and (min-width: 720px) and (max-width: 1249.98px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    @media screen and (max-width: 719.98px) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
}
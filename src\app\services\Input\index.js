import { API } from "@api";
import { createBase, deleteBase, getDetailBase, updateBase } from "@services/Base";
import { useAnalyticsEventTracker } from "@src/ga";


export function getInputDetail(id) {
  return getDetailBase(API.INPUT_ID, id);
}

export function createInput(data) {
  return createBase(API.INPUT, data);
}

export function updateDetailInput(data) {
  return updateBase(API.INPUT_ID, data);
}

export function updateInputMarkTest(data) {
  return updateBase(API.INPUT_UPDATE_MARK, data);
}

export function deleteInput(id) {
  return deleteBase(API.INPUT_ID, id);
}
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

import AntButton from "@component/AntButton";

import { BUTTON, PRICING_TYPE } from "@constant";

import PricingHeader from "./PricingHeader";
import PricingBusiness from "./PricingBusiness";
import PricingIndividual from "./PricingIndividual";

import "./Pricing.scss";

function Pricing() {
  const { t } = useTranslation();
  const [pricingType, setPricingType] = useState(PRICING_TYPE.INDIVIDUAL);

  return (
    <div className="pricing-container">
      <PricingHeader pricingType={pricingType} setPricingType={setPricingType} />

      <div className="pricing-type">
        {Object.values(PRICING_TYPE).map((type, index) => (
          <AntButton
            key={index}
            size="large"
            type={pricingType === type ? BUTTON.DEEP_NAVY : BUTTON.WHITE}
            onClick={() => setPricingType(type)}>
            {t(type)}
          </AntButton>
        ))}
      </div>

      <PricingBusiness pricingType={pricingType} />
      <PricingIndividual pricingType={pricingType} />
    </div>
  );
}

export default Pricing;

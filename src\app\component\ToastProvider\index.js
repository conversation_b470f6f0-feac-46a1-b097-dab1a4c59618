import { createRef, forwardRef, useImperativeHandle } from "react";
import { notification } from "antd";
import { useTranslation } from "react-i18next";

import "./ToastProvider.scss";

import SUCCESS_ICON from "@src/asset/icon/check-success/check.svg";
import WARNING_ICON from "@src/asset/icon/warning-yellow/warning.svg";
import ERROR_ICON from "@src/asset/icon/error/error.svg";
import ERROR_PERMISSION_ICON from "@src/asset/icon/error-access/error.svg";

import { CONSTANT } from "@constant";
import { sleep } from "@common/functionCommons";

export const toastRef = createRef();
export const toast = {
  success: (...args) => toastRef.current?.success(...args),
  error: (...args) => toastRef.current?.error(...args),
  warning: (args) => toastRef.current?.warning(args),
  denied: (...args) => toastRef.current?.denied(...args),
  destroy: (...args) => toastRef.current?.destroy(...args),
};

export const ToastProvider = forwardRef(function ToastProvider(props, ref) {
  const { t } = useTranslation();
  
  const [api, contextHolder] = notification.useNotification({
    placement: "bottomRight",
    bottom: 0,
  });
  
  
  const { TOAST_DURATION } = CONSTANT;
  
  useImperativeHandle(ref, () => ({
    success: renderSuccess,
    error: renderToastError,
    warning: renderToastWarning,
    denied: renderToastDenied,
    destroy: destroyToast,
  }));
  
  
  function renderToastDenied(description, options = {}) {
    const config = {
      message: t("DENIED"),
      description,
      icon: <img src={ERROR_PERMISSION_ICON} alt="" />,
      className: "notification-error",
      duration: TOAST_DURATION,
    };
    handleToggleToast(config, options);
  }
  
  async function handleToggleToast(config, options) {
    if (options.unique || !!options.replace) config.key = config.description;
    if (options.replace) {
      destroyToast(config.key);
      await sleep(100);
    }
    api.open(config);
  }
  
  function renderSuccess(descCode, options = {}) {
    const config = {
      message: t("SUCCESS"),
      icon: <img src={SUCCESS_ICON} alt="" />,
      className: "notification-success",
      duration: TOAST_DURATION,
      description: t(descCode),
    };
    handleToggleToast(config, options);
  }
  
  function renderToastError(descCode, options = {}) {
    const config = {
      message: t("ERROR"),
      icon: <img src={ERROR_ICON} alt="" />,
      className: "notification-error",
      duration: TOAST_DURATION,
      description: t(descCode),
    };
    handleToggleToast(config, options);
  }
  
  const renderToastWarning = ({ ...props }) => {
    api.open({
      message: t("WARNING"),
      icon: <img src={WARNING_ICON} alt="" />,
      className: "notification-warning",
      duration: TOAST_DURATION,
      ...props,
    });
  };
  
  function destroyToast(key) {
    api.destroy(key);
  }
  
  return (
    <>
      {contextHolder}
      {props.children}
    </>
  );
});

import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router-dom";

import { CONSTANT, DISCOUNT_TYPE, PACKAGE_TERM, PAYMEN_METHOD, PROMOTION_TYPE, BUTTON, PAYMENT_STATUS } from "@constant";

import { renderMoney } from "@src/common/functionCommons";
import dayjs from "dayjs";
import { Radio } from "antd";
import AntButton from "@src/app/component/AntButton";

const TransactionDetail = ({ ...props }) => {
  const { t } = useTranslation();
  const { transactionActive } = props;
  const { discountIds: applyDiscounts, state, vnpExpireDate, paymentUrl } = transactionActive || {};

  const priceData = useMemo(() => {
    if (!transactionActive) {
      return null;
    }
    let returnData = transactionActive?.subscriptionId?.packageId?.prices?.find(price => price?.unitName === transactionActive?.unitPrice && parseInt(price.intervalCount) === transactionActive?.intervalCount);
    let promotionalPrice = returnData?.unitAmount;
    const { type, discount } = transactionActive.promotionId || {};
    if (type === PROMOTION_TYPE.PERCENTAGE.value) {
      promotionalPrice = returnData?.unitAmount - (returnData?.unitAmount * discount / 100);
    } else if (type === PROMOTION_TYPE.FIXED.value) {
      promotionalPrice = returnData?.unitAmount - discount;
    }
    return { ...returnData, unitAmount: promotionalPrice };
  }, [transactionActive]);

  const { total, discountValues } = useMemo(() => {
    if (priceData) {
      let money = priceData.unitAmount;
      const discounts = applyDiscounts.map(discountData => {
        let discountValue = discountData.discount;
        if (discountData?.type === DISCOUNT_TYPE.PERCENTAGE) {
          discountValue = (money * discountData.discount) / 100;
        }
        money = money - discountValue;
        if (priceData.unitAmount - discountValue < 5000) {
          discountValue = priceData.unitAmount;
          money = 0;
        }
        return { code: discountData?.code, discount: discountValue }
      })
      return {
        total: money,
        discountValues: discounts
      }
    }
    return { total: 0, discountValues: [] }
  }, [priceData, applyDiscounts]);

  const formatDate = (dateTime) => {
    return dayjs(dateTime).format("DD-MM-YYYY");
  }

  const numberOfDays = useMemo(() => {
    if (transactionActive) {
      const startDate = dayjs(transactionActive?.subscriptionId?.startDate);
      const endDate = dayjs(transactionActive?.subscriptionId?.endDate);
      return endDate.diff(startDate, "day");
    }
    return 0;
  }, [transactionActive]);

  const isShowResume = useMemo(() => {
    if (state === PAYMENT_STATUS.PROCESSING && vnpExpireDate) {
      return dayjs().isBefore(dayjs(vnpExpireDate));
    }
  }, [state, vnpExpireDate]);

  function renderDescription() {
    return (<>
      {t('SERVICE_PACKAGE_START_FROM')}
      <span className="font-semibold">{formatDate(transactionActive?.subscriptionId?.startDate)}</span>
      {t('TO_DATE')}
      <span className="font-semibold">{formatDate(transactionActive?.subscriptionId?.endDate)}</span>
      {` (${numberOfDays} ${t("DAY_OF_USE")})`}
    </>)
  }

  return <div className="transaction-detail">
    <div className="transaction-detail__package-info">
      <div className="package-info__name">
        {transactionActive?.subscriptionId?.packageId?.name}
        <span className="package-info__unit-price">{`${transactionActive?.subscriptionId?.intervalCount} ${transactionActive?.unitPrice}`}</span>
      </div>
      <div className="package-info__term">
        {renderDescription()}
      </div>
    </div>
    <div className="transaction-detail__item">
      <div className="content__item__title ">{t('PAYMENT_INFO')}</div>
      <div className="payment-info">
        <div className="payment-info__item">
          <div className="payment-info__item__label">{t('SERVICE_PACKAGE')}</div>
          {renderMoney(Math.ceil(priceData?.unitAmount))}
        </div>
        <div className="payment-info__item">
          <div className="payment-info__item__label">{t('DISCOUNT')}</div>
          {renderMoney(Math.ceil(priceData?.unitAmount) - Math.ceil(total))}
        </div>
        {discountValues.map(discountValue => {
          return <div className="payment-info__item payment-info__discount" key={discountValue?.code}>
            <div className="payment-info__item__label">{discountValue?.code}</div>
            {renderMoney(Math.floor(discountValue?.discount))}
          </div>
        })}
        <div className="payment-info__item payment-info__total">
          <div className="payment-info__item__label">{t('TOTAL_PAYMENT')}</div>
          {renderMoney(Math.ceil(total))}
        </div>
      </div>
    </div>
    <div className="transaction-detail__item">
      <div className="content__item__title">{t("RECURRING_PAYMENT")}</div>
      <Radio className="automatic-payment-ratio" checked={transactionActive?.isRecurring}>
        {t("AUTOMATIC_PAYMENT")}
      </Radio>
    </div>
    <div className="transaction-detail__item">
      <div className="content__item__title">{t("PAYMENT_METHOD_UPPER")}</div>
      <div>{t(PAYMEN_METHOD[transactionActive?.paymentMethod?.toUpperCase()]?.lang)}</div>
    </div>
    <div className="transaction-detail__item">
      <div className="content__item__title">{t("TRANSACTION_CODE")}</div>
      <div>{transactionActive?._id}</div>
    </div>
    <div className="transaction-detail__item">
      <div className="content__item__title">{t("CONTACT_SUPPORT")}:</div>
      <div className="hotline-support">{t('HOT_LINE_CLICKEE')}</div>
    </div>
    {isShowResume && <Link to={paymentUrl} className="resume-payment-btn">
      <AntButton
        size='large'
        type={BUTTON.LIGHT_GREEN}
      >
        {t("RESUME_PAYMENT").toUpperCase()}
      </AntButton>
    </Link>}
  </div>
};

export default TransactionDetail;
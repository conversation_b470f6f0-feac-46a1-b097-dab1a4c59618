import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import { GENERAL_ACCESS_TYPE, PERMISSION } from "@constant";

import Share from "@src/app/component/Share";

import './WhoHasAccess.scss';

const WhoHasAccess = ({ user, ...props }) => {
  const { permission, generalAccess, userAccess, shareData, handleAfterShare } = props;
  const { t, i18n } = useTranslation();
  const [isShowModalShare, setShowmodalShare] = useState(false);

  const { access, shareWith, other } = useMemo(() => {
    if ((!generalAccess || generalAccess?.typeAccess === GENERAL_ACCESS_TYPE.RESTRICTED) && !userAccess.length) {
      return { access: t("PRIVATE_TO_YOU") };
    }

    let access, other = '', shareWith = '';
    if (generalAccess?.typeAccess === GENERAL_ACCESS_TYPE.ANYONE_WITH_LINK) {
      access = t("ANYONE_WITH_LINK");
    }
    if (generalAccess?.typeAccess === GENERAL_ACCESS_TYPE.ORGANIZATIONAL) {
      shareWith = " " + generalAccess?.organizationId?.name;
      if (userAccess.length) {
        shareWith += ",";
      }
    }
    if (userAccess.length) {
      let userNames = [];
      userAccess.slice(0, 3).forEach((item) => {
        userNames.push(item?.userId?.fullName);
      });
      shareWith += " " + userNames.join(", ");
      if (userAccess.length > 3) {
        const otherNumber = userAccess.length - 3;
        shareWith += " " + t("AND") + " ";
        other = otherNumber + " " + (otherNumber > 1 ? t("OTHERS").toLowerCase() : t("OTHER").toLowerCase());
      }
    }
    return { access, shareWith, other };
  }, [generalAccess, userAccess, i18n.language]);

  const toggleShare = () => {
    setShowmodalShare(!isShowModalShare);
  }

  if (permission === PERMISSION.VIEWER) {
    return null;
  }

  return (
    <div className="who-has-access">
      <div className="who-has-access__label">{t("WHO_HAS_ACCESS")}</div>
      <div className="who-has-access__value">
        {access && <div>{access}</div>}
        {shareWith && <div>
          {t("SHARED_WITH")}
          <span className="who-has-access__share-with">{shareWith}</span>
          {other && <span className="who-has-access__other" onClick={toggleShare}>
            {other}
          </span>}
        </div>}
      </div>
      <Share
        isShowModal={isShowModalShare}
        handleCancel={toggleShare}
        handleAfterShare={handleAfterShare}
        {...shareData}
        user={user}
      />
    </div>);
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(WhoHasAccess);
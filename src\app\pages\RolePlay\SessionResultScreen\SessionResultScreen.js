import React, { useEffect, useState, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, Spin, Alert, Typography, Descriptions, List, Tag, Divider, Button, Row, Col, Empty, Space, Avatar, Progress, Badge, Modal } from 'antd';
import { getRolePlaySessionDetail, getAnalysisForSession } from '@services/RolePlay/RolePlaySessionService';
import { useTranslation } from 'react-i18next';
import moment from 'moment';
import { ArrowLeftOutlined, ProfileOutlined, UserOutlined, MessageOutlined, InfoCircleOutlined, CheckCircleOutlined, ClockCircleOutlined, BookOutlined } from '@ant-design/icons';
import AudioPlayer from '@src/app/component/AudioPlayer/AudioPlayer';
import AntButton from '@src/app/component/AntButton';
import { BUTTON } from '@constant';

const { Title, Text, Paragraph } = Typography;

const SessionResultScreen = () => {
  const { sessionId, courseId: courseIdFromParams } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [sessionData, setSessionData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const analysisCheckIntervalRef = useRef(null);

  // State for Transcript Modal
  const [isTranscriptModalVisible, setIsTranscriptModalVisible] = useState(false);

  // State for Task Detail Modal
  const [isTaskDetailModalVisible, setIsTaskDetailModalVisible] = useState(false);
  const [currentTaskForModal, setCurrentTaskForModal] = useState(null); // For Task details

  // State for Persona Detail Modal
  const [isPersonaDetailModalVisible, setIsPersonaDetailModalVisible] = useState(false);

  // State for Course Detail Modal
  const [isCourseDetailModalVisible, setIsCourseDetailModalVisible] = useState(false);

  useEffect(() => {
    if (sessionId) {
      setLoading(true);
      setError(null);
      getRolePlaySessionDetail(sessionId)
        .then(data => {
          console.log('data', data);
          if (data) {
            setSessionData(data);
            // Check if analysis is already available
            if (data.status === 'analyzed' && data.analysisId && data.analysisId.result) {
              // Analysis is already available, no need to poll
              console.log('Analysis already available:', data.analysisId.result);
            } else {
              // Start polling for analysis results
              startAnalysisPolling(sessionId);
            }
          } else {
            setError(t('ERROR_FETCHING_SESSION_DETAIL', 'Không thể tải chi tiết phiên. Dữ liệu không hợp lệ.'));
          }
        })
        .catch(err => {
          console.error('Error fetching session detail:', err);
          setError(t('ERROR_FETCHING_SESSION_DETAIL_NETWORK', 'Lỗi khi tải chi tiết phiên. Vui lòng thử lại.'));
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
        console.warn('SessionId is undefined.');
        setError(t('SESSION_ID_UNDEFINED', 'Không tìm thấy mã phiên.'));
        setLoading(false);
    }

    // Cleanup function to clear interval when component unmounts
    return () => {
      if (analysisCheckIntervalRef.current) {
        clearInterval(analysisCheckIntervalRef.current);
        analysisCheckIntervalRef.current = null;
      }
    };
  }, [sessionId, t]);

  /**
   * Start polling for analysis results every 3 seconds
   * @param {string} sessionId - The ID of the session to check for analysis
   */
  const startAnalysisPolling = (sessionId) => {
    // Clear any existing interval
    if (analysisCheckIntervalRef.current) {
      clearInterval(analysisCheckIntervalRef.current);
    }

    // Set up new interval to check every 3 seconds
    analysisCheckIntervalRef.current = setInterval(() => {
      checkSessionAnalysis(sessionId);
    }, 3000); // 3 seconds
  };

  /**
   * Check if analysis results are available for the session
   * @param {string} sessionId - The ID of the session to check
   */
  const checkSessionAnalysis = (sessionId) => {
    console.log('Checking session analysis for:', sessionId);
    getAnalysisForSession(sessionId, false) // false to avoid showing loading indicator
      .then(data => {
        if (data && data.result) {
          console.log('Analysis results received:', data);
          // Stop polling once we have results
          if (analysisCheckIntervalRef.current) {
            clearInterval(analysisCheckIntervalRef.current);
            analysisCheckIntervalRef.current = null;
          }

          // Update session data with the analysis results
          setSessionData(prevData => {
            if (!prevData) return null;
            return {
              ...prevData,
              analysisId: data,
              status: 'analyzed'
            };
          });
        } else {
          console.log('Analysis not ready yet, will check again in 3 seconds');
        }
      })
      .catch(err => {
        console.error('Error checking session analysis:', err);
      });
  };

  // useEffect for fetching Analysis Data is REMOVED - Analysis data comes from sessionData.analysisId

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
        <div style={{ padding: '20px' }}>
            <Alert message={t('ERROR_TITLE', 'Lỗi')} description={error} type="error" showIcon />
            <AntButton type={BUTTON.TEXT} onClick={() => navigate(-1)} style={{ marginTop: '20px' }} icon={<ArrowLeftOutlined />}>
                {t('GO_BACK', 'Quay lại')}
            </AntButton>
        </div>
    );
  }

  if (!sessionData) {
    return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
            <Empty description={t('NO_SESSION_DATA_FOUND', 'Không tìm thấy dữ liệu cho phiên này.')} />
            <AntButton type={BUTTON.TEXT} onClick={() => navigate(-1)} style={{ marginTop: '20px' }} icon={<ArrowLeftOutlined />}>
                {t('GO_BACK', 'Quay lại')}
            </AntButton>
        </div>
    );
  }

  const {
    taskIds: sessionTaskObjectsFromData, // Renamed to avoid conflict
    courseId: sessionCourseInfoFromData, // Renamed
    transcripts: transcriptsFromData,     // Renamed
    status: sessionStatusFromData,        // Renamed
    endTime: sessionEndTimeFromData,      // Renamed
    analysisId: analysisIdFromData,       // Renamed
    // studentId: studentIdFromData, // If needed directly, rename too
    // createdBy: createdByFromData, // If needed directly, rename too
    // ... any other direct destructured props from sessionData that might conflict
  } = sessionData;

  // Use the renamed variables for subsequent logic
  const mainTaskObjectForDisplay = sessionTaskObjectsFromData?.find(t => t._id === sessionData.taskId); // sessionData.taskId is fine if not destructured and renamed
  const personaInfo = sessionCourseInfoFromData?.aiPersonaId;
  const finalCourseIdForNavigation = courseIdFromParams || sessionCourseInfoFromData?._id;

  const showTranscriptModal = () => {
    setIsTranscriptModalVisible(true);
  };

  const handleTranscriptModalClose = () => {
    setIsTranscriptModalVisible(false);
  };

  const showTaskDetailModal = (taskObject) => {
    setCurrentTaskForModal(taskObject);
    setIsTaskDetailModalVisible(true);
  };

  const handleTaskDetailModalClose = () => {
    setIsTaskDetailModalVisible(false);
    setCurrentTaskForModal(null);
  };

  const showPersonaDetailModal = () => {
    setIsPersonaDetailModalVisible(true);
  };

  const handlePersonaDetailModalClose = () => {
    setIsPersonaDetailModalVisible(false);
  };

  const showCourseDetailModal = () => {
    setIsCourseDetailModalVisible(true);
  };

  const handleCourseDetailModalClose = () => {
    setIsCourseDetailModalVisible(false);
  };

  // Helper to render analysis sections
  const renderAnalysisSection = () => {
    if (!sessionData) return null;

    // Use the renamed destructured variables directly, or access them via sessionData.analysisId.result etc.
    if (sessionStatusFromData === 'analyzed' && analysisIdFromData && analysisIdFromData.result) {
      const { result } = analysisIdFromData; // result is local to this function, so it's fine

      return (
        <div>
          <Row gutter={[24, 24]}>
            {/* Column 1: Top Insights (Conversation Simulation moved out) */}
            <Col xs={24} lg={16}>
              <Row gutter={[24, 24]}>
                  {/* Conversation Simulation Info Card REMOVED FROM HERE */}
                  <Col xs={24}>
                      <Card title={t('TOP_INSIGHTS', 'Top Insights')}>
                          {result.summary && <Paragraph style={{ fontStyle: 'italic' }}>{result.summary}</Paragraph>}
                          {result.topInsights && result.topInsights.length > 0 && (
                          <List
                              size="small"
                              dataSource={result.topInsights}
                              renderItem={item => <List.Item>- {item}</List.Item>}
                              style={{paddingLeft: '20px'}}
                          />
                          )}
                      </Card>
                  </Col>
              </Row>
            </Col>

            {/* Column 2: AI Score */}
            <Col xs={24} lg={8}>
              <Card style={{ textAlign: 'center', height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', background: result.simulationScore && result.simulationScore >= (sessionCourseInfoFromData?.passScore || 70) ? '#f0f9eb' : '#fff1f0' }}>
                <Text style={{ fontSize: '16px', color: '#666', display: 'block', marginBottom: '8px' }}>
                  {t('AI_SCORE_TITLE', 'AI Score')}
                </Text>
                <Text style={{ fontSize: '60px', color: result.simulationScore && result.simulationScore >= (sessionCourseInfoFromData?.passScore || 70) ? '#4CAF50' : '#D32F2F', fontWeight: 'bold', lineHeight: 1 }}>
                  {result.simulationScore ?? t('N/A', 'N/A')}
                </Text>
                <Paragraph style={{ marginTop: 12, color: '#666' }}>
                  {t('PASS_SCORE_DYNAMIC', 'Pass score: {0}').format(sessionCourseInfoFromData?.passScore || 70)}
                </Paragraph>
              </Card>
            </Col>
          </Row>

          <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
            {/* AI Trainer Feedback */}
            <Col xs={24}>
              <Card
                title={
                  <Space align="center">
                    <Avatar src={personaInfo?.avatarUrl || personaInfo?.avatar || undefined} icon={!(personaInfo?.avatarUrl || personaInfo?.avatar) && <UserOutlined />} />
                    <Text strong>{t('AI_TRAINER_FEEDBACK', 'AI Trainer Feedback')}</Text>
                  </Space>
                }
              >
                {result.trainerFeedback?.generalComments && (
                  <Paragraph>{result.trainerFeedback.generalComments}</Paragraph>
                )}
                {result.trainerFeedback?.improvementSuggestions && result.trainerFeedback.improvementSuggestions.length > 0 && (
                  <>
                    <Paragraph strong style={{ marginTop: 16 }}>{t('IMPROVEMENT_SUGGESTIONS', 'Gợi ý cải thiện:')}</Paragraph>
                    <List
                      size="small"
                      dataSource={result.trainerFeedback.improvementSuggestions}
                      renderItem={item => <List.Item>- {item}</List.Item>}
                      style={{paddingLeft: '20px'}}
                    />
                  </>
                )}
              </Card>
            </Col>
          </Row>

          <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
            {/* Knowledge & Proficiency */}
            <Col xs={24}>
              <Card>
                <Row justify="space-between" align="middle">
                  <Col><Title level={5} style={{margin:0}}>{t('KNOWLEDGE_ANALYSIS_TITLE', 'Phân tích Kiến thức')}</Title></Col>
                  {/* Removed overall knowledge score tag here, scores are per task */}
                </Row>
                <Paragraph type="secondary" style={{ marginBottom: 16 }}>
                  {result.knowledgeAnalysis?.knowledgeSubtitle || t('KNOWLEDGE_BREAKDOWN_SUBTITLE', 'Phân tích chi tiết theo từng nhiệm vụ trong khóa học.')}
                </Paragraph>

                {Array.isArray(result.knowledgeAnalysis?.taskAnalyses) && result.knowledgeAnalysis.taskAnalyses.length > 0 && (
                  <List
                    itemLayout="vertical"
                    dataSource={result.knowledgeAnalysis.taskAnalyses}
                    renderItem={(taskAnalysisItem, index) => {
                      const taskObjForThisAnalysis = sessionTaskObjectsFromData?.find(t => t._id === taskAnalysisItem.taskId);
                      return (
                        <List.Item key={taskAnalysisItem.taskId || index} style={{ paddingBottom: 24, borderBottom: index === result.knowledgeAnalysis.taskAnalyses.length - 1 ? 'none' : '1px solid #f0f0f0'}}>
                          <Row align="top" justify="space-between" style={{ marginBottom: 8}}>
                            <Col xs={24} sm={16}>
                              <Space align="center">
                                <Badge color={taskAnalysisItem.score && taskAnalysisItem.score >= (taskObjForThisAnalysis?.passScore || 70) ? 'green' : 'red'} /> {/* Example color based on score */}
                                <Text strong>{taskAnalysisItem.taskName || taskObjForThisAnalysis?.name || t('UNTITLED_TASK_ITEM', 'Nhiệm vụ không tên')}</Text>
                              </Space>
                            </Col>
                            <Col xs={24} sm={8} style={{ textAlign: 'right' }}>
                              <Space size="middle">
                                {taskObjForThisAnalysis && (
                                    <AntButton size="small" type={BUTTON.TEXT} onClick={() => showTaskDetailModal(taskObjForThisAnalysis)}>
                                        {t('VIEW_TASK_DETAILS_BTN', 'Chi tiết Nhiệm vụ')}
                                    </AntButton>
                                )}
                                {taskAnalysisItem.score !== undefined && <Tag color={taskAnalysisItem.score >= (taskObjForThisAnalysis?.passScore || 70) ? 'success' : taskAnalysisItem.score >= ((taskObjForThisAnalysis?.passScore || 70) * 0.8) ? 'warning' : 'error'} style={{fontWeight: 'bold'}}>{taskAnalysisItem.score}</Tag>}
                              </Space>
                            </Col>
                          </Row>
                          <Paragraph style={{ marginLeft: 22, whiteSpace: 'pre-wrap' }}>{taskAnalysisItem.details}</Paragraph>

                          {taskAnalysisItem.makeOrBreak && taskAnalysisItem.makeOrBreak.length > 0 && (
                            <div style={{ marginLeft: 22, marginTop: 8 }}>
                                <Paragraph strong>{t('KNOWLEDGE_MAKE_OR_BREAK', 'Yếu tố then chốt (Make or Break):')}</Paragraph>
                                <List size="small" dataSource={taskAnalysisItem.makeOrBreak} renderItem={mbItem => <List.Item>- {mbItem}</List.Item>} style={{ paddingLeft: '15px' }}/>
                            </div>
                          )}
                          {taskAnalysisItem.suggestions && taskAnalysisItem.suggestions.length > 0 && (
                            <div style={{ marginLeft: 22, marginTop: 8 }}>
                                <Paragraph strong>{t('KNOWLEDGE_SUGGESTIONS', 'Gợi ý cải thiện:')}</Paragraph>
                                <List size="small" dataSource={taskAnalysisItem.suggestions} renderItem={sugItem => <List.Item>- {sugItem}</List.Item>} style={{ paddingLeft: '15px' }}/>
                            </div>
                          )}
                          {taskObjForThisAnalysis?.helpfulLinks && taskObjForThisAnalysis.helpfulLinks.length > 0 && (
                            <div style={{ marginLeft: 22, marginTop: 10 }}>
                                <Paragraph strong>{t('HELPFUL_LINKS', 'Liên kết hữu ích')}:</Paragraph>
                                <ul style={{ paddingLeft: '15px', margin: 0 }}>
                                    {taskObjForThisAnalysis.helpfulLinks.map((link, linkIndex) => (
                                        <li key={linkIndex}><a href={link} target="_blank" rel="noopener noreferrer">{link}</a></li>
                                    ))}
                                </ul>
                            </div>
                          )}
                        </List.Item>
                      );
                    }}
                  />
                )}

                <Divider style={{ margin: '16px 0'}}/>

                <Title level={5} style={{margin:'0 0 8px 0'}}>{t('PROFICIENCY_TITLE', 'Proficiency')}</Title>
                <Paragraph type="secondary" style={{ marginBottom: 16 }}>
                  {t('PROFICIENCY_SUBTITLE', 'Mức độ bạn hiểu rõ vấn đề')}
                </Paragraph>
                {result.knowledgeAnalysis?.proficiencyProcess ? (
                  <Paragraph style={{ background: '#fafafa', padding: '10px', borderRadius: '4px', border: '1px solid #f0f0f0', whiteSpace: 'pre-wrap' }}>
                    {result.knowledgeAnalysis.proficiencyProcess}
                  </Paragraph>
                ) : <Progress percent={0} /> }
              </Card>
            </Col>
          </Row>
          {/* Placeholder for Style Analysis, Emotion Analysis etc. if they come from API */}
        </div>
      );
    } else if (sessionStatusFromData !== 'analyzed') {
        return (
          <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <Paragraph style={{ marginTop: 16, textAlign: 'center' }}>
              {t('ANALYSIS_LOADING', 'Đang phân tích kết quả phiên luyện tập...')}
            </Paragraph>
            <Paragraph type="secondary" style={{ textAlign: 'center' }}>
              {t('ANALYSIS_LOADING_DESC', 'Quá trình này có thể mất vài phút, vui lòng đợi.')}
            </Paragraph>
          </div>
        );
    } else {
        // Status is 'analyzed' but analysisIdFromData or analysisIdFromData.result is missing
        return <Empty description={t('NO_ANALYSIS_DATA_AVAILABLE', 'Không có dữ liệu phân tích cho phiên này.')} style={{ marginTop: 20 }} />;
    }
  };

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <Card
        title={
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>
                {t('SESSION_RESULT_TITLE', 'Kết quả Phiên Luyện Tập')}
              </Title>
            </Col>
            <Col>
              <AntButton
                type={BUTTON.TEXT}
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate(finalCourseIdForNavigation ? `/role-play/session/${finalCourseIdForNavigation}` : -1)}
              >
                {t('BACK_TO_COURSE_OR_PREVIOUS', 'Quay lại Khóa học / Trang trước')}
              </AntButton>
            </Col>
          </Row>
        }
        bordered={false}
        style={{ maxWidth: '1200px', margin: '0 auto' }}
      >
        {/* Conversation Simulation Info - MOVED HERE */}
        <Card title={t('CONVERSATION_SIMULATION_INFO_TITLE', 'Conversation Simulation')} style={{ marginBottom: 24 }}>
            <Descriptions column={1} size="small" layout="horizontal" colon={false}>
                <Descriptions.Item labelStyle={{width: '150px'}} label={t('COURSE', 'Course')}>
                    <Space>
                        <span>{sessionCourseInfoFromData?.name || t('N/A', 'N/A')}</span>
                        <Button size="small" type="text" icon={<InfoCircleOutlined />} onClick={showCourseDetailModal} aria-label={t('VIEW_COURSE_DETAILS_ARIA', 'View course details')} />
                    </Space>
                </Descriptions.Item>
                {personaInfo && (
                    <Descriptions.Item labelStyle={{width: '150px'}} label={t('AI_PERSONA_IN_SIMULATION', 'AI Persona')}>
                        <Space>
                            <span>{personaInfo.name || t('N/A', 'N/A')} {personaInfo.role ? `(${personaInfo.role})` : ''}</span>
                            <Button size="small" type="text" icon={<InfoCircleOutlined />} onClick={showPersonaDetailModal} aria-label={t('VIEW_PERSONA_DETAILS_ARIA', 'View AI persona details')} />
                        </Space>
                    </Descriptions.Item>
                )}
                <Descriptions.Item labelStyle={{width: '150px'}} label={t('CONVERSATION_HISTORY', 'Conversation History')}>
                    <Space>
                        <span>{`${transcriptsFromData?.length || 0} ${t('TURNS', 'lượt')}`}</span>
                        {transcriptsFromData && transcriptsFromData.length > 0 &&
                            <Button size="small" type="text" icon={<MessageOutlined />} onClick={showTranscriptModal} aria-label={t('VIEW_TRANSCRIPT_ARIA', 'View conversation history')} />
                        }
                    </Space>
                </Descriptions.Item>
                <Descriptions.Item labelStyle={{width: '150px'}} label={t('DATE_COMPLETED', 'Date')}>
                    {sessionEndTimeFromData ? moment(sessionEndTimeFromData).format('DD MMM YYYY') : t('N/A', 'N/A')}
                </Descriptions.Item>
            </Descriptions>
        </Card>

        {/* Task Detail Modal */}
        <Modal
            title={<><ProfileOutlined style={{ marginRight: 8 }} /> {currentTaskForModal?.name || t('TASK_DETAILS_MODAL_TITLE', 'Chi tiết Nhiệm Vụ')}</>}
            open={isTaskDetailModalVisible}
            onOk={handleTaskDetailModalClose}
            onCancel={handleTaskDetailModalClose}
            width={700}
            footer={[
                <AntButton key="close" type={BUTTON.TEXT} onClick={handleTaskDetailModalClose}>
                    {t('CLOSE_MODAL', 'Đóng')}
                </AntButton>,
            ]}
        >
            <Card type="inner" title={t('TASK_INFORMATION', 'Thông tin Nhiệm Vụ')} style={{ border: 0, boxShadow: 'none' }}>
                <Descriptions column={1} bordered>
                    <Descriptions.Item label={t('TASK_NAME_LABEL', 'Tên nhiệm vụ')}>{currentTaskForModal?.name || t('N/A', 'N/A')}</Descriptions.Item>
                    <Descriptions.Item label={t('DESCRIPTION', 'Mô tả')}>
                        <pre style={{ whiteSpace: 'pre-wrap', margin:0, fontFamily: 'inherit' }}>{currentTaskForModal?.description || t('N/A', 'N/A')}</pre>
                    </Descriptions.Item>
                    <Descriptions.Item label={t('EVALUATION_GUIDELINES', 'Hướng dẫn đánh giá')}>
                        <pre style={{ whiteSpace: 'pre-wrap', margin:0, fontFamily: 'inherit' }}>
                            {currentTaskForModal?.evaluationGuidelines || t('N/A', 'N/A')}
                        </pre>
                    </Descriptions.Item>
                </Descriptions>
                {currentTaskForModal?.exampleVideoUrl && (
                    <Paragraph style={{ marginTop: 16 }}>
                        <strong>{t('EXAMPLE_VIDEO', 'Video mẫu')}:</strong> <a href={currentTaskForModal.exampleVideoUrl} target="_blank" rel="noopener noreferrer">{currentTaskForModal.exampleVideoUrl}</a>
                    </Paragraph>
                )}
                {currentTaskForModal?.helpfulLinks && currentTaskForModal.helpfulLinks.length > 0 && (
                    <div style={{ marginTop: 16 }}>
                        <Paragraph strong>{t('HELPFUL_LINKS', 'Liên kết hữu ích')}:</Paragraph>
                        <List
                            size="small"
                            bordered
                            dataSource={currentTaskForModal.helpfulLinks}
                            renderItem={(link, index) => (
                                <List.Item key={index}><a href={link} target="_blank" rel="noopener noreferrer">{link}</a></List.Item>
                            )}
                        />
                    </div>
                )}
                 {!currentTaskForModal?.description && !currentTaskForModal?.evaluationGuidelines && !currentTaskForModal?.exampleVideoUrl && (!currentTaskForModal?.helpfulLinks || currentTaskForModal?.helpfulLinks.length === 0) && (
                    <Empty description={t('NO_TASK_DETAILS_AVAILABLE', 'Không có chi tiết cho nhiệm vụ này.')} />
                 )}
            </Card>
        </Modal>

        {/* AI Persona Detail Modal */}
        {personaInfo && (
            <Modal
                title={<><UserOutlined style={{ marginRight: 8 }}/> {t('PERSONA_DETAILS_MODAL_TITLE', 'Chi tiết AI Persona')}</>}
                open={isPersonaDetailModalVisible}
                onOk={handlePersonaDetailModalClose}
                onCancel={handlePersonaDetailModalClose}
                width={600}
                footer={[
                    <AntButton key="close" type={BUTTON.TEXT} onClick={handlePersonaDetailModalClose}>
                        {t('CLOSE_MODAL', 'Đóng')}
                    </AntButton>,
                ]}
            >
                 <Card type="inner" title={`${t('PERSONA_INFO_FOR', 'Thông tin về')} ${personaInfo.name} ${personaInfo.role ? `(${personaInfo.role})` : ''}`} style={{ border:0, boxShadow: 'none'}}>
                    <Descriptions column={1} bordered size="small" layout="vertical">
                        <Descriptions.Item label={t('MOOD', 'Tâm trạng')}>{personaInfo.mood || t('N/A', 'N/A')}</Descriptions.Item>
                        <Descriptions.Item label={t('ORGANIZATION', 'Tổ chức')}>{personaInfo.organization || t('N/A', 'N/A')}</Descriptions.Item>
                        <Descriptions.Item label={t('BACKGROUND', 'Bối cảnh' )}><pre style={{ whiteSpace: 'pre-wrap', margin:0, fontFamily: 'inherit' }}>{personaInfo.personaBackground || t('N/A', 'N/A')}</pre></Descriptions.Item>
                        <Descriptions.Item label={t('CONCERN', 'Mối quan tâm' )}><pre style={{ whiteSpace: 'pre-wrap', margin:0, fontFamily: 'inherit' }}>{personaInfo.personaConcern || t('N/A', 'N/A')}</pre></Descriptions.Item>
                    </Descriptions>
                </Card>
            </Modal>
        )}

        {/* Course Detail Modal */}
        <Modal
            title={<><BookOutlined style={{ marginRight: 8 }} /> {t('COURSE_DETAILS_MODAL_TITLE', 'Chi Tiết Khóa Học')}</>}
            open={isCourseDetailModalVisible}
            onOk={handleCourseDetailModalClose}
            onCancel={handleCourseDetailModalClose}
            width={700}
            footer={[
                <AntButton key="close" type={BUTTON.TEXT} onClick={handleCourseDetailModalClose}>
                    {t('CLOSE_MODAL', 'Đóng')}
                </AntButton>,
            ]}
        >
            {sessionCourseInfoFromData ? (
                <Card type="inner" title={t('COURSE_INFORMATION', 'Thông tin Khóa Học')} style={{ border: 0, boxShadow: 'none' }}>
                    <Title level={5}>{sessionCourseInfoFromData.name || t('N/A', 'N/A')}</Title>
                    {sessionCourseInfoFromData.description && (
                        <Paragraph style={{marginTop: 8}}>
                            <strong>{t('COURSE_DESCRIPTION_LABEL', 'Mô tả khóa học')}:</strong>
                            <pre style={{ whiteSpace: 'pre-wrap', margin:0, fontFamily: 'inherit' }}>{sessionCourseInfoFromData.description}</pre>
                        </Paragraph>
                    )}
                    {/* Display tasks within the course */}
                    {sessionTaskObjectsFromData && sessionTaskObjectsFromData.length > 0 && (
                       <div style={{marginTop: 16}}>
                            <Paragraph strong>{t('TASKS_IN_THIS_COURSE_LABEL', 'Các nhiệm vụ trong khóa học này')}:</Paragraph>
                            <List
                                size="small"
                                bordered
                                dataSource={sessionTaskObjectsFromData}
                                renderItem={taskItem => (
                                    <List.Item key={taskItem._id}>
                                        <List.Item.Meta
                                            title={taskItem.name}
                                            description={<pre style={{ whiteSpace: 'pre-wrap', margin:0, fontFamily: 'inherit' }}>{taskItem.description}</pre>}
                                        />
                                        {/* Optionally show button to view this task's details from analysis if available */}
                                    </List.Item>
                                )}
                            />
                       </div>
                    )}
                    {(sessionCourseInfoFromData.referenceUrls || sessionCourseInfoFromData.referenceMaterial) && (sessionCourseInfoFromData.referenceUrls?.length > 0 || sessionCourseInfoFromData.referenceMaterial?.length > 0) && (
                        <div style={{marginTop: 16}}>
                            <Paragraph strong>{t('REFERENCE_MATERIAL_LABEL', 'Tài liệu tham khảo')}:</Paragraph>
                            <List
                                size="small"
                                bordered
                                dataSource={[...(sessionCourseInfoFromData.referenceUrls || []), ...(sessionCourseInfoFromData.referenceMaterial || [])]}
                                renderItem={(item, index) => {
                                    const url = typeof item === 'string' ? item : item?.url;
                                    const text = typeof item === 'string' ? item : (item?.name || item?.title || url);
                                    if (!url) return <List.Item key={index}>{t('INVALID_MATERIAL_ITEM', 'Tài liệu không hợp lệ')}</List.Item>;
                                    return (
                                        <List.Item key={index}>
                                            <a href={url} target="_blank" rel="noopener noreferrer">{text}</a>
                                        </List.Item>
                                    );
                                }}
                            />
                        </div>
                    )}
                    {!sessionCourseInfoFromData.description && (!sessionTaskObjectsFromData || sessionTaskObjectsFromData.length === 0) && (!sessionCourseInfoFromData.referenceUrls || sessionCourseInfoFromData.referenceUrls.length === 0) && (!sessionCourseInfoFromData.referenceMaterial || sessionCourseInfoFromData.referenceMaterial.length === 0) && (
                         <Paragraph>{t('NO_COURSE_DETAILS_AVAILABLE', 'Không có chi tiết bổ sung cho khóa học này.')}</Paragraph>
                    )}
                </Card>
            ) : (
                <Empty description={t('NO_COURSE_INFO_LOADED', 'Không thể tải thông tin khóa học.')} />
            )}
        </Modal>

        {/* Transcript Modal */}
        <Modal
            title={<><MessageOutlined style={{ marginRight: 8 }} /> {t('TRANSCRIPT_MODAL_TITLE', 'Lịch sử hội thoại')}</>}
            open={isTranscriptModalVisible}
            onOk={handleTranscriptModalClose}
            onCancel={handleTranscriptModalClose}
            width={800}
            footer={[
                <AntButton key="close" type={BUTTON.TEXT} onClick={handleTranscriptModalClose}>
            {t('CLOSE_MODAL', 'Đóng')}
            </AntButton>,
            ]}
        >
            {transcriptsFromData && transcriptsFromData.length > 0 ? (
            <List
                itemLayout="horizontal"
                dataSource={transcriptsFromData}
                renderItem={(item, index) => (
                <List.Item key={item._id || index} style={{ alignItems: 'flex-start' }}>
                    <List.Item.Meta
                    avatar={
                        <Tag color={item.role === 'student' ? 'blue' : 'green'} style={{ marginRight: 8 }}>
                        {item.role === 'student' ? t('YOU', 'Bạn') : (personaInfo?.name || t('AI', 'AI'))}
                        </Tag>
                    }
                    title={
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                            {moment(item.timestamp).format('HH:mm:ss, DD/MM/YYYY')}
                        </Text>
                    }
                    description={
                        <>
                        <Paragraph style={{ marginBottom: '5px', whiteSpace: 'pre-wrap' }}>{item.content}</Paragraph>
                        {item.audioId && (
                            <AudioPlayer audioId={item.audioId} smallPlayer />
                        )}
                        </>
                    }
                    />
                </List.Item>
                )}
                style={{ background: '#fff', padding: '0 16px', borderRadius: '8px', maxHeight: '60vh', overflowY: 'auto' }}
            />
            ) : (
            <Empty description={t('NO_TRANSCRIPT_AVAILABLE', 'Không có lịch sử hội thoại.')} />
            )}
        </Modal>

        {renderAnalysisSection()}
      </Card>
    </div>
  );
};

export default SessionResultScreen;

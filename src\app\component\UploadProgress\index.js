import React from "react";
import { Progress } from "antd";
import PropTypes from "prop-types";

import AntButton from "@component/AntButton";

import { fileExtImage, humanFileSize } from "@common/functionCommons";

import Close from "@component/SvgIcons/Close";

import "./UploadProgress.scss";

function UploadProgress({ fileBlob, percent, onCancel, disabledCancel = false }) {
  if (!fileBlob) return <></>;
  return <div className="upload-progress-container">
    <div className="upload-progress-wrapper">
      <div className="upload-progress__file">
        <div className="upload-progress__file-icon">
          {fileExtImage(fileBlob)}
        </div>
        <div className="upload-progress__file-upload">
          <div className="upload-progress__file-info">
            <div className="upload-progress__file-name" title={fileBlob.name}>
              {fileBlob.name}
            </div>
            <div className="upload-progress__file-capacity">
              {humanFileSize(fileBlob.size)}
            </div>
          </div>
          {!!percent && <div className="upload-progress__file-progress">
            <Progress percent={percent} />
          </div>}
        </div>
      </div>
      <div className="upload-progress__close">
        <AntButton
          shape="circle"
          icon={<Close />}
          onClick={onCancel}
          disabled={disabledCancel}
        />
      </div>
    </div>
  </div>;
}


UploadProgress.defaultProps = {
  onCancel: () => null,
};


UploadProgress.propTypes = {
  onCancel: PropTypes.func,
};

export default UploadProgress;
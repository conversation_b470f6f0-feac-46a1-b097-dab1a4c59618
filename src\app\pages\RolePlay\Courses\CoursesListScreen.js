import React, {useState, useEffect, useRef} from 'react';
import {Input, Select, Card, Button, Spin, Empty} from 'antd';
// import SortArrowIconUrl from '@src/assets/icons/sort-arrow.svg'; // Assuming you have a similar icon
// import SearchIconUrl from '@src/assets/icons/search.svg'; // Assuming you have a similar icon
import DefaultCourseImageUrl from '@src/asset/image/project-default.svg'; // Placeholder, replace with actual default course image
import {useNavigate, useLocation} from 'react-router-dom';
import {LINK} from '@src/constants/link'; // Sử dụng đường dẫn LINK
import {useTranslation} from 'react-i18next';
import {getAllCourses} from '@src/app/services/RolePlay'; // Import service từ RolePlay
import './CoursesListScreen.scss';
import Loading from '@src/app/component/Loading';
import { API } from '@src/constants/api';

const {Option} = Select;
const PAGE_SIZE = 12; // Adjust as needed
const START_PAGE = 1;

export const CoursesListScreen = () => {
  const {t} = useTranslation();
  const [courses, setCourses] = useState([]);
  const [allCourses, setAllCourses] = useState([]); // For maintaining a copy of all courses if needed for client-side filtering
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState(null); // For filtering by course type (Sale, Service, HR, Education)
  const [sortOrder, setSortOrder] = useState('asc'); // 'asc' for A-Z, 'desc' for Z-A
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(START_PAGE);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const navigate = useNavigate();
  const location = useLocation();
  const searchTimeout = useRef(null);

  // Xây dựng query parameters cho API
  const buildQuery = () => {
    const query = {};
    if (searchQuery) {
      query.search = searchQuery;
    }
    if (selectedType) {
      query.type = selectedType;
    }
    if (sortOrder) {
      query.sort = sortOrder === 'asc' ? 'name' : '-name';
    }
    return query;
  };

  // Xây dựng thông tin phân trang
  const buildPaging = () => {
    return {
      page,
      limit: PAGE_SIZE,
    };
  };

  // Cập nhật URL params
  const updateUrlParams = () => {
    const params = new URLSearchParams();
    if (searchQuery) params.set('search', searchQuery);
    if (selectedType) params.set('type', selectedType);
    if (sortOrder) params.set('sort', sortOrder);
    if (page !== START_PAGE) params.set('page', page.toString());

    const newUrl = `${location.pathname}?${params.toString()}`;
    window.history.replaceState({}, '', newUrl);
  };

  // Hàm lấy danh sách khóa học từ API
  const fetchCourses = async () => {
    try {
      setLoading(true);
      const query = buildQuery();
      const paging = buildPaging();

      const response = await getAllCourses({...query, status: 'published'}, paging);

      if (response && response.rows) {
        const formattedCourses = response.rows.map(course => ({
          id: course._id,
          title: course.name,
          description: course.description,
          time: course.estimatedCallTimeInMinutes ? `~ ${course.estimatedCallTimeInMinutes} min` : 'N/A',
          progress: '0% Completed', // API hiện không có trường này, tạm ẩn hoặc để default
          image: course?.thumbnailId ? API.STREAM_ID.format(course?.thumbnailId) : DefaultCourseImageUrl, // Hoặc nếu có trường avatarId/imageUrl thì dùng: course.imageUrl || (course.avatarId ? `${window.location.origin}/api/files/content/${course.avatarId}` : DefaultCourseImageUrl)
          type: course.simulationType, // API trả về simulationType
        }));

        if (page === START_PAGE) {
          setCourses(formattedCourses);
        } else {
          setCourses(prev => [...prev, ...formattedCourses]);
        }

        setAllCourses(prev => (page === START_PAGE ? formattedCourses : [...prev, ...formattedCourses]));
        setTotalCount(response.count || 0);
        setHasMore(page * PAGE_SIZE < response.count);
      } else {
        setCourses([]);
        setAllCourses([]);
        setTotalCount(0);
        setHasMore(false);
      }
    } catch (error) {
      console.error('Error fetching courses:', error);
      setCourses([]);
      setAllCourses([]);
      setTotalCount(0);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  };

  // Gọi API khi component mount hoặc khi các filter thay đổi
  useEffect(() => {
    // Cập nhật URL params
    updateUrlParams();

    // Gọi API để lấy danh sách khóa học
    fetchCourses();
  }, [page, searchQuery, selectedType, sortOrder]);

  const handleSearch = value => {
    // Clear any existing timeout
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    // Set a timeout to prevent too many API calls while typing
    searchTimeout.current = setTimeout(() => {
      setSearchQuery(value);
      setPage(START_PAGE);
    }, 500);
  };

  const handleTypeChange = value => {
    setSelectedType(value);
    setPage(START_PAGE);
  };

  const toggleSort = () => {
    const newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    setSortOrder(newSortOrder);
    setPage(START_PAGE);
  };

  const handleCourseClick = courseId => {
    // Giả sử mỗi khóa học có một nhiệm vụ mặc định với ID "task1"
    // Trong thực tế, bạn có thể lấy danh sách nhiệm vụ và chọn nhiệm vụ đầu tiên hoặc hiển thị danh sách nhiệm vụ
    navigate(LINK.ROLE_PLAY_SESSION.format(courseId));
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      setPage(prevPage => prevPage + 1);
    }
  };

  // Icons - replace with actual imports if you have them
  const SearchIconUrl =
    "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E";
  const SortArrowIconUrl =
    "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z'/%3E%3C/svg%3E";

  return (
    <div className="courses-list-screen">
      <div className="courses-list-screen__container">
        <div className="courses-list-screen__header">
          <div className="courses-list-screen__search-container">
            <Input
              className="courses-list-screen__search-input"
              placeholder={t('SEARCH_COURSE_PLACEHOLDER', 'Search courses...')} // Example translation key
              suffix={<img src={SearchIconUrl} alt="Search" className="courses-list-screen__search-icon" />}
              onChange={e => handleSearch(e.target.value)}
              value={searchQuery}
            />
            <Select
              className="courses-list-screen__type-select"
              placeholder={t('COURSE_TYPE_PLACEHOLDER', 'Course Type')} // Example translation key
              onChange={handleTypeChange}
              allowClear
              value={selectedType}
            >
              <Option value="Sale">{t('COURSE_TYPE_SALE', 'Sale')}</Option>
              <Option value="Service">{t('COURSE_TYPE_SERVICE', 'Service')}</Option>
              <Option value="HR">{t('COURSE_TYPE_HR', 'HR')}</Option>
              <Option value="Education">{t('COURSE_TYPE_EDUCATION', 'Education')}</Option>
            </Select>
          </div>
          <Button className="courses-list-screen__sort-button" onClick={toggleSort}>
            {sortOrder === 'asc' ? 'A-Z' : 'Z-A'}
            <img
              src={SortArrowIconUrl}
              alt="Sort direction"
              className={`courses-list-screen__sort-icon ${
                sortOrder === 'desc' ? 'courses-list-screen__sort-icon--desc' : ''
              }`}
            />
          </Button>
        </div>

        {loading && courses.length === 0 ? (
          <div className="courses-list-screen__loading">
            <Loading active transparent />
          </div>
        ) : !loading && courses.length === 0 ? (
          <Empty
            description={t('NO_COURSES_FOUND', 'No courses found.')} // Example translation key
            className="courses-list-screen__empty"
          />
        ) : (
          <>
            <div className="courses-list-screen__courses-grid">
              {courses.map(course => (
                <Card
                  key={course.id}
                  className="courses-list-screen__course-card"
                  hoverable
                  onClick={() => handleCourseClick(course.id)}
                  cover={
                    <img
                      className="course-image"
                      alt={course.title}
                      src={course.image}
                      onError={e => {
                        e.target.onerror = null; // prevents looping
                        e.target.src = DefaultCourseImageUrl;
                      }}
                    />
                  }
                >
                  <div className="course-card__content-wrapper">
                    <h3 className="course-title">{course.title}</h3>
                    <p className="course-time">{course.time}</p>
                    <p className="course-progress">{course.progress}</p>
                    <p className="course-description">{course.description}</p>
                  </div>
                  {/* <Button
                    className="courses-list-screen__action-button"
                    onClick={() => handleCourseClick(course.id)}
                  >
                    {t("START_COURSE", "Start Course")}
                  </Button> */}
                </Card>
              ))}
            </div>

            {hasMore && (
              <div className="courses-list-screen__load-more">
                <Button
                  onClick={handleLoadMore}
                  loading={loading && courses.length > 0} // Show loading on button only if loading more
                  className="courses-list-screen__load-more-button"
                >
                  {t('LOAD_MORE', 'Load More')}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

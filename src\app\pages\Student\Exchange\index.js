import { useState, useEffect } from 'react';
import { connect } from "react-redux";

import TransactionDetail from './TransactionDetail';
import TransactionList from './TransactionList';

import { getAllTransactionByUser } from '@services/Transaction';

import './Exchange.scss';
import Loading from '@src/app/component/Loading';

const Exchange = ({ user, ...props }) => {
  const [transactionData, setTransactionData] = useState([]);
  const [transactionActive, setTransactionActive] = useState();
  const [isLoading, setLoading] = useState(false);

    useEffect(() => {
      getTransactionData();
    }, []);

  const getTransactionData = async () => {
    setLoading(true);
    const dataResponse = await getAllTransactionByUser();
    if (dataResponse) {
      setTransactionData(dataResponse.map(transaction => ({ ...transaction, key: transaction._id })));
      setTransactionActive(dataResponse[0]);
    }
    setLoading(false);
  };

  const onChangeTransaction = (transaction) => {
    setTransactionActive(transaction);
  }

  return <Loading active={isLoading} className="exchange-container">
    <TransactionList
      transactionData={transactionData}
      onChangeTransaction={onChangeTransaction}
      transactionActive={transactionActive}
    />
    {transactionActive && <TransactionDetail transactionActive={transactionActive} />}
  </Loading>
}

const mapStateToProps = (state) => {
  const { user } = state.auth;
  return { user };
}
export default connect(mapStateToProps)(Exchange);
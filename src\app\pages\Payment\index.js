import { useEffect, useState } from "react";
import { useParams, useLocation, matchPath } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import ProcessBar from "./ProcessBar";
import PaymentContent from "./PaymenContent";
import PaymentComplete from "./PaymentComplete";

import { PAYMENT_STATUS, USER_TYPE, VNPAY_RESPONSE_ERROR } from "@constant";
import { LINK } from "@link";

import { getPackageDetail } from "@src/app/services/Package";
import { streamTransactionPayment } from "@src/app/services/Payment";

import * as auth from "@src/ducks/auth.duck";

import './Payment.scss';
import PaymentProcessing from "./PaymentProcessing";
import PaymentContentStudent from "./PaymenContent/PaymentContentStudent";

const Payment = ({ user, ...props }) => {
  const { id } = useParams();
  const { search, pathname } = useLocation();
  const { t } = useTranslation();
  const isStudent = user.type === USER_TYPE.STUDENT;

  const [activeStep, setActiveStep] = useState(2);
  const [packageData, setPackageData] = useState();
  const [transactionState, setTransactionState] = useState({
    isSuccess: true,
    messageLang: '',
    transactionId: null
  });

  let transactionEvent;

  useEffect(() => {
    handlePathname();
    return () => closeEv();
  }, [pathname]);

  const getPackageData = async () => {
    const response = await getPackageDetail(id);
    if (response) setPackageData(response);
  }

  const handlePathname = () => {
    if (matchPath(LINK.PAYMENT_VNPAY, pathname)) {
      const params = new URLSearchParams(search);
      const { transactionId, vnp_ResponseCode } = Object.fromEntries(params.entries());
      if (vnp_ResponseCode === '00') {
        setActiveStep(4);
        getTransactionData(transactionId);
      } else {
        setActiveStep(5);
        setTransactionState({
          isSuccess: false,
          messageLang: VNPAY_RESPONSE_ERROR[vnp_ResponseCode],
          transactionId: transactionId
        })
      }
    } else {
      getPackageData();
    }
  }

  const getTransactionData = async (transactionId) => {
    transactionEvent = streamTransactionPayment(transactionId);
    transactionEvent.addEventListener(transactionId, async (event) => {
      const data = JSON.parse(event?.data);
      setActiveStep(5);
      if (data?.state === PAYMENT_STATUS.ERROR) {
        setTransactionState({
          isSuccess: false,
          messageLang: t('PAYMENT_FAILED'),
          transactionId: transactionId
        })
      }
    })

    transactionEvent.addEventListener("error", async (error) => {
      closeEv();
    });
  }

  function closeEv() {
    transactionEvent?.close();
  }

  return <div className="payment">
    <div className="payment__body">
      <ProcessBar activeStep={activeStep} />
      < div className="payment__content">
        {activeStep === 2 && (isStudent ? <PaymentContentStudent packageData={packageData} setActiveStep={setActiveStep} />
          : <PaymentContent user={user} packageData={packageData} />)}
        {activeStep === 4 && <PaymentProcessing />}
        {activeStep === 5 && <PaymentComplete transactionState={transactionState} />}
      </div>
    </div>
  </div >;
};

const mapStateToProps = (state) => {
  const { user } = state.auth;
  return { user };
};

const connector = connect(mapStateToProps, { ...auth.actions });

export default connector(Payment);
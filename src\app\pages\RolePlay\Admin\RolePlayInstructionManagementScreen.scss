.roleplay-instruction-container {
  .roleplay-instruction-info-card,
  .roleplay-instruction-search-card,
  .roleplay-instruction-table-card {
    margin-bottom: 24px;
  }

  .roleplay-instruction-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .roleplay-instruction-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }

    .roleplay-instruction-description {
      margin: 8px 0 0;
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .form-filter {
    .search-form-item {
      margin-bottom: 0;
    }

    .search-buttons-col {
      display: flex;
      justify-content: flex-end;

      .search-buttons {
        display: flex;
        gap: 8px;
      }
    }
  }

  .roleplay-instruction-actions {
    display: flex;
    justify-content: center;
    gap: 8px;

    .btn-edit,
    .btn-delete {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .roleplay-instruction-table {
    .name-value {
      font-weight: 500;
    }

    .roleplay-instruction-table-row {
      &:hover {
        cursor: pointer;
      }
    }
  }
}
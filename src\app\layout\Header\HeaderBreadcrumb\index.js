import { useMemo } from "react";
import { Breadcrumb } from "antd";
import { Link, matchPath, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { connect } from "react-redux";

import BREADCRUMB from "@src/constants/breadcrumb";

import DOT from "@src/asset/icon/dot/dot.svg";

const SEPARATOR = <img src={DOT} alt=""/>;

function HeaderBreadcrumb({ user, ...props }) {
  const { t } = useTranslation();
  const { pathname } = useLocation();

  const breadcrumbItems = useMemo(() => {
    const breadcrumbList = [];

    // Check if BREADCRUMB is defined
    if (!BREADCRUMB || !Array.isArray(BREADCRUMB)) {
      return breadcrumbList;
    }

    // Find matching breadcrumb for current path
    BREADCRUMB.forEach(breadcrumb => {
      // Check if breadcrumb and path are valid
      if (!breadcrumb || typeof breadcrumb.path !== 'string' || !breadcrumb.items) {
        return;
      }

      const match = matchPath(breadcrumb.path, pathname);
      if (match) {
        // Process each item in the breadcrumb
        breadcrumb.items.forEach(item => {
          if (!item) return;

          let url = item.url;
          let title = item.lang ? t(item.lang) : '';

          // Handle dynamic URLs with parameters
          if (url && typeof url === 'string' && url.includes(':id') && match.params && match.params.id) {
            url = url.replace(':id', match.params.id);
          }

          if (url && typeof url === 'string' && url.includes(':courseId') && match.params && match.params.courseId) {
            url = url.replace(':courseId', match.params.courseId);
          }

          if (url && typeof url === 'string' && url.includes(':taskId') && match.params && match.params.taskId) {
            url = url.replace(':taskId', match.params.taskId);
          }

          if (title) {
            breadcrumbList.push({
              title: url ? <Link to={url}>{title}</Link> : title
            });
          }
        });
      }
    });

    return breadcrumbList;
  }, [t, pathname]);
  
  return <div className="header-breadcrumb">
    <Breadcrumb
      separator={SEPARATOR}
      items={[
        { type: "separator", separator: SEPARATOR },
        ...breadcrumbItems,
      ]}
    />
  </div>;
}

function mapStateToProps(store) {
  const { user } = store.auth;
  return { user };
}

export default connect(mapStateToProps)(HeaderBreadcrumb);
import React, { useRef, useEffect } from 'react';

const CustomLiveAudioVisualizer = ({
  audioData, // Expects a Float32Array
  width = 300,
  height = 75,
  barWidth = 2,
  gap = 1,
  barColor = '#f76565',
  backgroundColor = 'transparent',
}) => {
  const canvasRef = useRef(null);

  useEffect(() => {
    if (!canvasRef.current) {
      return;
    }

    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    context.clearRect(0, 0, width, height);

    // Draw background
    if (backgroundColor !== 'transparent') {
      context.fillStyle = backgroundColor;
      context.fillRect(0, 0, width, height);
    }

    // Determine if audio is silent or not present
    const silenceThreshold = 0.005; // Ngưỡng xác định im lặng
    let isEffectivelySilent = true;

    if (audioData && audioData.length > 0) {
      let maxAmplitude = 0;
      for (let k = 0; k < audioData.length; k++) {
        if (Math.abs(audioData[k]) > maxAmplitude) {
          maxAmplitude = Math.abs(audioData[k]);
        }
      }
      if (maxAmplitude >= silenceThreshold) {
        isEffectivelySilent = false;
      }
    }
    // Nếu audioData là null, undefined, hoặc rỗng, nó cũng được coi là im lặng để vẽ đường thẳng.

    if (isEffectivelySilent) {
      // Vẽ một đường thẳng ngang
      const lineY = height / 2;
      context.beginPath();
      context.moveTo(0, lineY);
      context.lineTo(width, lineY);
      context.strokeStyle = barColor; // Sử dụng barColor cho đường thẳng
      context.lineWidth = 1; // Độ dày của đường thẳng
      context.stroke();
    } else {
      // Logic vẽ cột hiện tại (nếu audioData hợp lệ và không im lặng)
      context.fillStyle = barColor;
      const numBars = Math.floor((width - gap) / (barWidth + gap));

      if (numBars <= 0) return; // Không có chỗ để vẽ cột

      const dataPointsPerBar = Math.floor(audioData.length / numBars);

      for (let i = 0; i < numBars; i++) {
        let maxVal = 0;
        const startIndex = i * dataPointsPerBar;
        const endIndex = Math.min(startIndex + dataPointsPerBar, audioData.length);

        if (startIndex < endIndex) { // Đảm bảo có dữ liệu để xử lý
          for (let j = startIndex; j < endIndex; j++) {
            if (Math.abs(audioData[j]) > maxVal) {
              maxVal = Math.abs(audioData[j]);
            }
          }

          let barHeightValue = maxVal * height;

          // Đảm bảo chiều cao tối thiểu cho các âm thanh rất nhỏ (nhưng không phải im lặng)
          if (barHeightValue > 0 && barHeightValue < 1) {
            barHeightValue = 1;
          }
          // Giới hạn chiều cao cột bằng chiều cao canvas
          barHeightValue = Math.min(barHeightValue, height);

          if (barHeightValue > 0) { // Chỉ vẽ nếu chiều cao cột > 0
              const x = gap + i * (barWidth + gap);
              const y = (height - barHeightValue) / 2; // Căn giữa cột theo chiều dọc
              context.fillRect(x, y, barWidth, barHeightValue);
          }
        }
      }
    }
  }, [audioData, width, height, barWidth, gap, barColor, backgroundColor]);

  return <canvas ref={canvasRef} width={width} height={height} />;
};

export default CustomLiveAudioVisualizer;

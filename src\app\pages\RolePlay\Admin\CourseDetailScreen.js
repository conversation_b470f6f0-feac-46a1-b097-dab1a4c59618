import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Card, Col, Form, Input, InputNumber, Row, Select, Upload, Button, Space, Typography, message} from 'antd';
import {useNavigate, useParams} from 'react-router-dom';
import {UploadOutlined, PlusOutlined, DeleteOutlined, FileTextOutlined} from '@ant-design/icons';
import {v4 as uuidv4} from 'uuid';

import {LINK} from '@link';
import {BUTTON} from '@constant';

import AntButton from '@component/AntButton';
import Loading from '@component/Loading';
import {AntForm} from '@component/AntForm';
import {toast} from '@component/ToastProvider';
import UploadImagePreview from '@component/UploadImagePreview'; // Giả sử có component này hoặc sẽ tạo

import {getCourseDetails, createCourse, updateCourse} from '@services/RolePlay/CourseService';
import {getAllAIPersonasWithoutPagination} from '@services/RolePlay/AIPersonaService';
import {uploadFile, deleteFile} from '@services/File';
import {getAllInstruction} from '@services/RolePlay/RolePlayInstructionService';

import CreateAIPersonaModal from './components/CreateAIPersonaModal';
import ReferenceContentModal from './components/ReferenceContentModal';

import {AIPersonaSetupCard} from '../AIPersona/AIPersonaSetupCard';
import {TaskEvaluationDisplayCard} from '../Tasks/TaskEvaluationDisplayCard';
import UploadReference from './UploadReference';
import { API } from "@api";

import './CourseDetailScreen.scss';

const {TextArea} = Input;
const {Option} = Select;
const {Title} = Typography;

const SIMULATION_TYPES = ['Sale', 'Service', 'HR', 'Education', 'Other'];

const CourseDetailScreen = () => {
  const {id} = useParams();
  const navigate = useNavigate();
  const {t} = useTranslation();
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  const [aiPersonas, setAiPersonas] = useState([]);
  const [selectedPersonaForPreview, setSelectedPersonaForPreview] = useState(null);
  const [taskDetailsList, setTaskDetailsList] = useState([]);

  // State cho roleplayInstructions
  const [roleplayInstructions, setRoleplayInstructions] = useState([]);
  const [selectedSimulationType, setSelectedSimulationType] = useState('');

  // Thay thế state cũ bằng state mới để quản lý references
  const [references, setReferences] = useState([]);

  // State cho modal upload reference
  const [isShowUploadReference, setShowUploadReference] = useState(false);

  // State cho modal create AI Persona
  const [isShowCreateAIPersona, setShowCreateAIPersona] = useState(false);

  // State cho modal hiển thị nội dung reference
  const [isShowReferenceContent, setShowReferenceContent] = useState(false);
  const [selectedReference, setSelectedReference] = useState(null);

  // Thumbnail related state
  const [thumbnailId, setThumbnailId] = useState(null);
  const [initialThumbnailIdOnLoad, setInitialThumbnailIdOnLoad] = useState(null);
  const [isLoadingThumbnail, setIsLoadingThumbnail] = useState(false);

  useEffect(() => {
    fetchAllAIPersonas();
    fetchAllRoleplayInstructions();
  }, []);

  useEffect(() => {
    if (id) {
      setIsEditMode(true);
      fetchCourseDetails(id);
    } else {
      setIsEditMode(false);
      form.resetFields();
      setThumbnailId(null);
      setInitialThumbnailIdOnLoad(null);
      setSelectedPersonaForPreview(null);
      setTaskDetailsList([]);
      form.setFieldsValue({thumbnailId: null});
    }
  }, [id, form]);

  useEffect(() => {
    form.setFieldsValue({thumbnailId: thumbnailId});
  }, [thumbnailId, form]);

  // Lắng nghe thay đổi của simulationType để lọc roleplayInstructions
  useEffect(() => {
    const simulationType = form.getFieldValue('simulationType');
    setSelectedSimulationType(simulationType);
  }, [form]);

  const fetchAllRoleplayInstructions = async () => {
    try {
      const instructionsData = await getAllInstruction();
      if (instructionsData) {
        setRoleplayInstructions(instructionsData);
      } else {
        setRoleplayInstructions([]);
        toast.error(t('ERROR_LOADING_ROLEPLAY_INSTRUCTIONS'));
      }
    } catch (error) {
      console.error('Error fetching roleplay instructions:', error);
      setRoleplayInstructions([]);
      toast.error(t('ERROR_LOADING_ROLEPLAY_INSTRUCTIONS'));
    }
  };

  const fetchAllAIPersonas = async () => {
    try {
      // Gọi API không phan trang, chỉ lấy các trường cần thiết cho danh sách và preview card
      // Giả định rằng AIPersonaSetupCard có thể hiển thị avatar chỉ với avatarId (string)
      const personasData = await getAllAIPersonasWithoutPagination({}, [
        // Bỏ populate cho avatarId nếu AIPersonaSetupCard có thể dùng string ID
        // { path: 'avatarId', select: 'url name _id' }
      ]);
      if (personasData) {
        setAiPersonas(personasData);
      } else {
        setAiPersonas([]); // Nếu không có dữ liệu hoặc lỗi, đặt thành mảng rỗng
        toast.error(t('ERROR_LOADING_AI_PERSONAS'));
      }
    } catch (error) {
      console.error('Error fetching AI personas:', error);
      setAiPersonas([]);
      toast.error(t('ERROR_LOADING_AI_PERSONAS'));
    }
  };

  const fetchCourseDetails = async courseId => {
    setIsLoading(true);
    try {
      const populateOpts = ['aiPersonaId', 'taskIds', 'references', 'roleplayInstructionId'];
      const apiResponse = await getCourseDetails(courseId, populateOpts);
      if (apiResponse) {
        form.setFieldsValue({
          ...apiResponse,
          aiPersonaId: apiResponse.aiPersonaId?._id, // Chỉ lấy _id cho Select
          roleplayInstructionId: apiResponse.roleplayInstructionId?._id, // Chỉ lấy _id cho Select
          // Không cần set referenceUrls nữa vì sẽ sử dụng state references
          // taskIds sẽ được xử lý riêng vào taskDetailsList
        });

        if (apiResponse.simulationType) {
          setSelectedSimulationType(apiResponse.simulationType);
        }

        if (apiResponse.aiPersonaId) {
          setSelectedPersonaForPreview(apiResponse.aiPersonaId);
        }

        if (apiResponse.thumbnailId) {
          setThumbnailId(apiResponse.thumbnailId); // Giả sử thumbnailId là một string ID
          setInitialThumbnailIdOnLoad(apiResponse.thumbnailId);
          form.setFieldsValue({thumbnailId: apiResponse.thumbnailId});
        } else {
          setThumbnailId(null);
          setInitialThumbnailIdOnLoad(null);
          form.setFieldsValue({thumbnailId: null});
        }

        // Cập nhật state references từ API response
        if (apiResponse.references && apiResponse.references.length > 0) {
          setReferences(apiResponse.references);
        } else {
          setReferences([]);
        }

        if (apiResponse.taskIds && apiResponse.taskIds.length > 0) {
          const formattedTasks = apiResponse.taskIds.map(task => ({
            _id: task._id, // Sử dụng _id từ DB làm key duy nhất
            key: task._id,
            name: task.name,
            evaluationGuidelines: task.evaluationGuidelines,
            weight: task.weight,
            exampleVideoUrl: task.exampleVideoUrl || '', // Đảm bảo khớp tên trường
            helpfulLinks: task.helpfulLinks || [],
            isMakeOrBreak: task.isMakeOrBreak || false,
            description: task.description || '',
            orderInCourse: task.orderInCourse,
          }));
          setTaskDetailsList(formattedTasks);
        } else {
          setTaskDetailsList([]);
        }
      } else {
        toast.error(t('ERROR_LOADING_COURSE_DATA'));
        navigate(LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT);
      }
    } catch (error) {
      console.error('Error fetching course details:', error);
      toast.error(t('ERROR_LOADING_COURSE_DATA'));
      navigate(LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUploadThumbnail = async file => {
    setIsLoadingThumbnail(true);
    try {
      const uploadResponse = await uploadFile(file, {folder: 'course_thumbnails'});
      if (uploadResponse && uploadResponse._id) {
        setThumbnailId(uploadResponse._id);
        toast.success(t('UPLOAD_THUMBNAIL_SUCCESS'));
      } else {
        toast.error(t('UPLOAD_THUMBNAIL_ERROR'));
      }
    } catch (error) {
      console.error('Thumbnail upload error:', error);
      toast.error(t('UPLOAD_THUMBNAIL_ERROR'));
    } finally {
      setIsLoadingThumbnail(false);
    }
  };

  const handleClearThumbnail = () => {
    setThumbnailId(null);
  };

  // Xử lý khi thêm references từ modal
  const handleReferenceAdded = addedReferences => {
    // Cập nhật state references với references mới được thêm từ modal
    setReferences(prevReferences => {
      // Lọc ra các references đã tồn tại để tránh trùng lặp
      const existingIds = prevReferences.map(ref => ref._id);
      const newReferences = addedReferences.filter(ref => !existingIds.includes(ref._id));
      return [...prevReferences, ...newReferences];
    });
  };

  const handleAiPersonaChange = value => {
    if (value) {
      const selected = aiPersonas.find(p => p._id === value);
      setSelectedPersonaForPreview(selected);
    } else {
      setSelectedPersonaForPreview(null);
    }
  };

  const handleSimulationTypeChange = value => {
    setSelectedSimulationType(value);
    // Reset roleplayInstructionId khi thay đổi simulationType
    form.setFieldsValue({roleplayInstructionId: undefined});
  };

  const handleAIPersonaCreated = async newPersona => {
    // Fetch lại danh sách AI Personas và chọn AI Persona vừa tạo
    await fetchAllAIPersonas();
    if (newPersona && newPersona._id) {
      form.setFieldsValue({aiPersonaId: newPersona._id});
      const selected = {...newPersona};
      setSelectedPersonaForPreview(selected);
    }
  };

  const handleAddTask = newTask => {
    setTaskDetailsList(prevTasks => [...prevTasks, newTask]);
  };

  const handleTaskUpdate = updatedTaskRow => {
    setTaskDetailsList(prevTasks =>
      prevTasks.map(task => {
        if (updatedTaskRow.original_temp_id && task._id === updatedTaskRow.original_temp_id) {
          // Đây là trường hợp task mới được tạo và có ID thật từ server
          // Bỏ original_temp_id trước khi lưu vào state
          const {original_temp_id, ...taskFromServer} = updatedTaskRow;
          return taskFromServer; // Cập nhật task với ID thật và dữ liệu mới từ server
        } else if (task._id === updatedTaskRow._id) {
          // Đây là trường hợp cập nhật task hiện có, hoặc cập nhật cell của task mới (vẫn dùng temp_id)
          return {...task, ...updatedTaskRow};
        }
        return task;
      }),
    );
  };

  const handleTaskDelete = taskIdToDelete => {
    setTaskDetailsList(prevTasks => prevTasks.filter(task => task._id !== taskIdToDelete));
  };

  const handleFormSubmit = async values => {
    setIsLoading(true);

    // Validate tasks
    if (taskDetailsList.some(task => !task.name || !task.evaluationGuidelines)) {
      message.error(
        t('VALIDATE_TASK_DETAILS_ERROR', 'Please fill in Topic Name and Evaluation Guidelines for all tasks.'),
      );
      setIsLoading(false);
      return;
    }
    const totalWeight = taskDetailsList.reduce((sum, task) => sum + (Number(task.weight) || 0), 0);
    if (taskDetailsList.length > 0 && totalWeight !== 100) {
      message.error(t('VALIDATE_TASK_WEIGHT_ERROR', 'The sum of weights for all tasks must be 100%.'));
      setIsLoading(false);
      return;
    }

    let oldThumbnailIdToDelete = null;
    if (initialThumbnailIdOnLoad && initialThumbnailIdOnLoad !== values.thumbnailId) {
      oldThumbnailIdToDelete = initialThumbnailIdOnLoad;
    }

    // Lấy danh sách ID của references
    const referenceIds = references.map(ref => ref._id);

    const payloadToSend = {
      ...values,
      thumbnailId: values.thumbnailId, // Đã được sync từ state
      references: referenceIds, // Sử dụng references mới thay vì referenceUrls và referenceFiles
      taskIds: taskDetailsList.map(
        ({
          _id,
        }) => _id,
      ),
    };

    try {
      let apiResponse;
      if (isEditMode) {
        apiResponse = await updateCourse({...payloadToSend, _id: id});
      } else {
        apiResponse = await createCourse(payloadToSend);
      }

      if (apiResponse) {
        if (oldThumbnailIdToDelete) {
          try {
            await deleteFile(oldThumbnailIdToDelete);
          } catch (deleteError) {
            console.error('Failed to delete old thumbnail:', oldThumbnailIdToDelete, deleteError);
          }
        }

        if (isEditMode) {
          toast.success(t('UPDATE_COURSE_SUCCESS'));
          fetchCourseDetails(id); // Tải lại để cập nhật UI
        } else {
          toast.success(t('CREATE_COURSE_SUCCESS'));
          if (apiResponse._id) {
            navigate(LINK.ADMIN.ROLE_PLAY_COURSE_DETAIL.format(apiResponse._id));
          }
        }
      } else {
        if (isEditMode) {
          toast.error(t('UPDATE_COURSE_ERROR'));
        } else {
          toast.error(t('CREATE_COURSE_ERROR'));
        }
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error(t('AN_ERROR_OCCURRED'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    navigate(LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT);
  };

  const handleEditPersona = persona => {
    // Chuyển hướng đến trang chi tiết AI Persona để chỉnh sửa
    if (persona && persona._id) {
      navigate(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_DETAIL.format(persona._id));
    }
  };

  const handleLearnMorePersona = () => {
    navigate(LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT); // Hoặc link tới trang tạo mới persona
  };

  // Xử lý xóa reference
  const handleRemoveReference = referenceId => {
    setReferences(prevReferences => prevReferences.filter(ref => ref._id !== referenceId));
  };

  // Lọc danh sách roleplayInstructions theo simulationType
  const filteredRoleplayInstructions = roleplayInstructions.filter(
    instruction => !selectedSimulationType || instruction.simulationType === selectedSimulationType
  );

  const saveButtonDisabled = isLoading || isLoadingThumbnail;

  return (
    <Loading active={isLoading} transparent>
      <div className="course-detail-container">
        <Card className="course-detail-header-card">
          <div className="course-detail-header">
            <div>
              <h1 className="course-detail-title">{isEditMode ? t('EDIT_COURSE') : t('CREATE_COURSE')}</h1>
              <p className="course-detail-description">
                {isEditMode ? t('EDIT_COURSE_DESCRIPTION') : t('CREATE_COURSE_DESCRIPTION')}
              </p>
            </div>
          </div>
        </Card>

        <AntForm
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
          className="course-detail-form-card"
          initialValues={{status: 'draft'}}
        >
          <Card title={t('BASIC_INFORMATION')}>
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="name"
                  label={t('COURSE_NAME', 'Course Name')}
                  rules={[
                    {required: true, message: t('PLEASE_INPUT_COURSE_NAME', 'Please input the course name!')},
                    {max: 255, message: t('COURSE_NAME_TOO_LONG', 'Course name cannot exceed 255 characters')},
                  ]}
                >
                  <Input placeholder={t('ENTER_COURSE_NAME', 'Enter course name')} />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="estimatedCallTimeInMinutes"
                  label={t('ESTIMATED_CALL_TIME', 'Estimated Call Time (minutes)')}
                  rules={[
                    {type: 'number', min: 0, message: t('TIME_MUST_BE_POSITIVE', 'Time must be a positive number')},
                  ]}
                >
                  <InputNumber
                    style={{width: '100%'}}
                    placeholder={t('ENTER_ESTIMATED_TIME', 'Enter estimated time in minutes')}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="status"
                  label={t('COURSE_STATUS', 'Course Status')}
                  rules={[{required: true, message: t('PLEASE_SELECT_COURSE_STATUS', 'Please select course status!')}]}
                >
                  <Select placeholder={t('SELECT_COURSE_STATUS', 'Select course status')} allowClear>
                    <Option value="draft">{t('DRAFT', 'Draft')}</Option>
                    <Option value="published">{t('PUBLISHED', 'Published')}</Option>
                    <Option value="archived">{t('ARCHIVED', 'Archived')}</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item label={t('COURSE_THUMBNAIL')}>
              <UploadImagePreview
                imageId={thumbnailId} // Sử dụng thumbnailId đã được fetch hoặc upload
                onDrop={handleUploadThumbnail}
                onClear={handleClearThumbnail}
                loading={isLoadingThumbnail}
                // Cần truyền URL của ảnh nếu thumbnailId chỉ là ID và không phải object File đầy đủ có URL
                // imageUrl={thumbnailId && typeof thumbnailId === 'object' && thumbnailId.url ? thumbnailId.url : (typeof thumbnailId === 'string' ? `/api/file/preview/${thumbnailId}` : null)}
              />
            </Form.Item>
            <Form.Item name="thumbnailId" hidden>
              <Input />
            </Form.Item>

            <Form.Item
              name="description"
              label={t('COURSE_DESCRIPTION', 'Course Description')}
              rules={[{max: 5000, message: t('DESCRIPTION_TOO_LONG', 'Description cannot exceed 5000 characters')}]}
            >
              <TextArea rows={4} placeholder={t('ENTER_COURSE_DESCRIPTION', 'Enter course description')} />
            </Form.Item>
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="simulationType"
                  label={t('SIMULATION_TYPE', 'Simulation Type')}
                  rules={[
                    {required: true, message: t('PLEASE_SELECT_SIMULATION_TYPE', 'Please select simulation type!')},
                  ]}
                >
                  <Select
                    placeholder={t('SELECT_SIMULATION_TYPE', 'Select simulation type')}
                    allowClear
                    onChange={handleSimulationTypeChange}
                  >
                    {SIMULATION_TYPES.map(type => (
                      <Option key={type} value={type}>
                        {t(type.toUpperCase() + '_SIMULATION', type)}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="roleplayInstructionId"
                  label={t('ROLEPLAY_INSTRUCTION', 'Roleplay Instruction')}
                  rules={[
                    {required: true, message: t('PLEASE_SELECT_ROLEPLAY_INSTRUCTION', 'Please select roleplay instruction!')},
                  ]}
                >
                  <Select
                    placeholder={t('SELECT_ROLEPLAY_INSTRUCTION', 'Select roleplay instruction')}
                    allowClear
                    disabled={!selectedSimulationType}
                    loading={!roleplayInstructions.length}
                    showSearch
                    filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                  >
                    {filteredRoleplayInstructions.map(instruction => (
                      <Option key={instruction._id} value={instruction._id}>
                        {instruction.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>
          {isEditMode && (
            <Card title={t('REFERENCE_MATERIALS', 'Reference Materials')} style={{marginTop: 24, marginBottom: 24}}>
              <div className="reference-materials-section">
                {/* Hiển thị danh sách references */}
                <div className="reference-section-header">
                  <h3>{t('REFERENCE_MATERIALS', 'Reference Materials')}</h3>
                </div>

                <div className="reference-list">
                  {references.map(reference => (
                    <div key={reference._id} className="reference-item">
                      {reference.type === 'url' ? (
                        <a href={reference.url} target="_blank" rel="noopener noreferrer">
                          {reference.name || reference.url}
                        </a>
                      ) : (
                        <a href={API.STREAM_ID.format(reference.fileId)} target="_blank" rel="noopener noreferrer">
                          {reference.name}
                        </a>
                      )}
                      <div className="reference-actions">
                        {reference.content && (
                          <Button
                            type="text"
                            icon={<FileTextOutlined />}
                            onClick={() => {
                              setSelectedReference(reference);
                              setShowReferenceContent(true);
                            }}
                            className="reference-view-btn"
                            title={t('VIEW_CONTENT')}
                          />
                        )}
                        <Button
                          type="text"
                          icon={<DeleteOutlined />}
                          onClick={() => handleRemoveReference(reference._id)}
                          className="reference-delete-btn"
                          title={t('DELETE')}
                        />
                      </div>
                    </div>
                  ))}
                </div>

                {/* Nút thêm tài liệu tham khảo */}
                <div className="add-reference-button">
                  <AntButton
                    size={'large'}
                    type={BUTTON.DEEP_NAVY}
                    onClick={() => setShowUploadReference(true)}
                    icon={<PlusOutlined />}
                  >
                    {t('ADD_REFERENCE_MATERIALS', 'Add Reference Materials')}
                  </AntButton>
                </div>
              </div>
            </Card>
          )}
          {isEditMode && (
            <Card>
              <Row xs={24} md={12} style={{justifyContent: 'space-between'}}>
                <Form.Item name="aiPersonaId" label={t('AI_PERSONA', 'AI Persona')}>
                  <Select
                    placeholder={t('SELECT_AI_PERSONA', 'Select AI Persona')}
                    loading={!aiPersonas.length}
                    allowClear
                    onChange={handleAiPersonaChange}
                    showSearch
                    filterOption={(input, option) => option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                  >
                    {aiPersonas.map(persona => (
                      <Option key={persona._id} value={persona._id}>
                        {persona.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <AntButton
                  type={BUTTON.DEEP_NAVY}
                  onClick={() => setShowCreateAIPersona(true)}
                  size={'large'}
                  icon={<PlusOutlined />}
                >
                  {t('ADD_AI_PERSONA', 'Add AI Persona')}
                </AntButton>
              </Row>
              {selectedPersonaForPreview && (
                <Row gutter={24} style={{marginBottom: '24px'}}>
                  <Col xs={24}>
                    <AIPersonaSetupCard
                      persona={selectedPersonaForPreview}
                      onEdit={handleEditPersona} // Chuyển đến trang edit persona
                      onLearnMore={handleLearnMorePersona} // Chuyển đến trang danh sách persona
                    />
                  </Col>
                  {/* <Col xs={24}>
                    <Form.Item
                      name="introduction"
                      label={t('COURSE_INTRODUCTION', 'Course Introduction (Tasks & Overview)')}
                      rules={[
                        {max: 5000, message: t('INTRODUCTION_TOO_LONG', 'Introduction cannot exceed 5000 characters')},
                      ]}
                    >
                      <TextArea
                        rows={4}
                        placeholder={t('ENTER_COURSE_INTRODUCTION', 'Enter course introduction and task overview')}
                      />
                    </Form.Item>
                  </Col> */}
                </Row>
              )}
            </Card>
          )}

          {isEditMode && (
            <TaskEvaluationDisplayCard
              dataSource={taskDetailsList}
              onTaskAdd={handleAddTask}
              onTaskUpdate={handleTaskUpdate}
              onTaskDelete={handleTaskDelete}
              courseId={id}
              style={{marginTop: 24}}
            />
          )}

          <Row justify="end" style={{marginTop: 24}} gutter={16}>
            <Col>
              <AntButton
                size={'large'}
                type={BUTTON.TEXT}
                onClick={handleCancel}
                className="btn-cancel"
                disabled={saveButtonDisabled}
              >
                {t('CANCEL')}
              </AntButton>
            </Col>
            <Col>
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size={'large'}
                htmlType="submit"
                className="btn-save"
                loading={isLoading && !isLoadingThumbnail}
                disabled={saveButtonDisabled}
              >
                {isEditMode ? t('SAVE_CHANGES') : t('CREATE_COURSE')}
              </AntButton>
            </Col>
          </Row>
        </AntForm>

        {/* Modal Upload Reference */}
        <UploadReference
          isShowUpload={isShowUploadReference}
          setShowUpload={setShowUploadReference}
          onReferenceAdded={handleReferenceAdded}
          courseId={id} // Truyền courseId từ params
        />

        {/* Modal Create AI Persona */}
        <CreateAIPersonaModal
        isOpen={isShowCreateAIPersona}
        onClose={() => setShowCreateAIPersona(false)}
        onSuccess={handleAIPersonaCreated}
        courseId={id} // Truyền courseId từ params
      />

      {/* Modal hiển thị nội dung reference */}
      <ReferenceContentModal
        isOpen={isShowReferenceContent}
        onClose={() => setShowReferenceContent(false)}
        reference={selectedReference}
      />
      </div>
    </Loading>
  );
};

export default CourseDetailScreen;

import { useTranslation } from "react-i18next";
import { <PERSON> } from "react-router-dom";

const PricingHeader = (props) => {
  const { t, i18n } = useTranslation();
  return (
    <div className="pricing-header">
      <span className="pricing-header__title">{t("UPGRADE_PLAN")}</span>
      <div className="pricing-header__info">
        <div>
          <div className="pricing-header__info__text">
            {t("BY_UPGRADE_TO_CLICKEE_PLAN")}
            {i18n.language === "vi" ? (
              <>
                <Link to="https://clickee.ai/dieu-khoan-su-dung/" target="_blank" className="pricing-header__terms">
                  {t("TEARM_OF_SERVICE")}
                </Link>
                <span>{t("OUR").toLowerCase()}.</span>
              </>
            ) : (
              <>
                <span>{t("OUR").toLowerCase()}</span>
                <Link to="https://clickee.ai/en/term-of-service/" target="_blank" className="pricing-header__terms">
                  {t("TEARM_OF_SERVICE")}.
                </Link>
              </>
            )}
          </div>
        </div>
        <div>
          <div className="pricing-header__info__text">
            <span>{t("NOTE")}:</span>
            {i18n.language === "vi" ? (
              <Link to="https://clickee.ai/chinh-sach-bao-mat/" target="_blank" className="pricing-header__terms">
                {t("PRIVACE_POLICY")}{" "}
              </Link>
            ) : (
              <>
                <span>{t("OUR")}</span>
                <Link to="https://clickee.ai/en/privacy-policy-2/" target="_blank" className="pricing-header__terms">
                  {t("PRIVACE_POLICY")}
                </Link>
              </>
            )}
            {t("EXPLAINS_HANDLE_SERVICE")}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingHeader;

import { Radio } from 'antd';
import { useTranslation } from "react-i18next";

import CREDIT_CARD_ICON from "@src/asset/icon/credit-card/credit-card-navy.svg";

export default function RecurringPayment(props) {
  const { t } = useTranslation();
  const { isRecurring, setRecurring } = props;

  const onChangeCheckbox = () => {
    setRecurring(pre => !pre);
  };

  return (<div className="payment-package-item">
    <div className="payment-package-recurring">
      <div className="payment-package-item__label">
        <img src={CREDIT_CARD_ICON} alt="" />
        {t('RECURRING_PAYMENT')}
      </div>
      <Radio checked={isRecurring} onClick={onChangeCheckbox}>
        {t('AUTOMATIC_PAYMENT')}
      </Radio>

    </div>
  </div>)
}
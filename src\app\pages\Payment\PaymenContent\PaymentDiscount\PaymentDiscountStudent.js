import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Input } from "antd";

import AntButton from "@src/app/component/AntButton";

import { checkDiscountCode } from "@services/Discount";

import { BUTTON } from "@constant";

import DANGER_ICON from "@src/asset/icon/danger/danger.svg";
import CLOSE_NAVY_ICON from "@src/asset/icon/close/close-navy.svg";
import CLOSE_ICON from "@src/asset/icon/close/close.svg";
import clsx from "clsx";

export default function PaymentDiscountStudent({ ...props }) {
  const { applyDiscounts, setApplyDiscounts } = props;
  const { t } = useTranslation();

  const [newCode, setNewCode] = useState('');
  const [isInvalidCode, setInValidCode] = useState(false);

  useEffect(() => {
    if (!newCode) setInValidCode(false);
  }, [newCode]);

  const onApplyDicountCode = async () => {
    const response = await checkDiscountCode({ code: newCode });
    if (response) {
      setApplyDiscounts([response]);
      setInValidCode(false);
      setNewCode('');
    } else {
      setInValidCode(true);
    }
  }

  const onChangeDiscountCode = (e) => {
    setNewCode(e.target.value);
  }

  const onRemoveDiscount = (discountId) => {
    setApplyDiscounts((prev) => prev.filter(discount => discount._id !== discountId));
  }

  return <div className="payment-package-item">
    <div className="payment-package-discount">
      <div className="payment-package-item__label">{t('DISCOUNT_CODE')}</div>
      <div className={clsx("payment-package-discount__discount-input", { "payment-package-discount__discount-input--invalid": isInvalidCode })}>
        <Input
          onChange={onChangeDiscountCode}
          placeholder={t('DISCOUNT_CODE_PLACEHOLDER')}
          value={newCode}
          suffix={<img src={CLOSE_ICON} alt="" onClick={() => setNewCode('')} />}
          onPressEnter={onApplyDicountCode} />
        <AntButton
          type={BUTTON.DEEP_NAVY}
          size="large"
          disabled={!newCode}
          onClick={onApplyDicountCode}>
          {t('APPLY')}
        </AntButton>
      </div>
      {isInvalidCode && <div className="payment-package-discount__notification">
        <img src={DANGER_ICON} alt="" />
        <div>
          {t('DISCOUNT_CODE_INVALID_NOTIFICATION')}
          <span className="payment-package-discount__policy-link">{t('DISCOUNT_POLICY')}</span>
        </div>
      </div>}
      {!!applyDiscounts.length && <div className="payment-package-discount__discount-list">
        {applyDiscounts.map((discount, index) => {
          return <span key={index} className="discount-list__discount-item">
            {discount?.code}
            <img onClick={() => onRemoveDiscount(discount._id)} src={CLOSE_NAVY_ICON} alt="" />
          </span>
        })}
      </div>}
    </div>
  </div>
}
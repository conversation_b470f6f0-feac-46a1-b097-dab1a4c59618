.modal-change-password {

  .modal-body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 24px;
  }

  .auth-content__title {
    font-size: 32px;
    font-weight: 700;
    line-height: 42px;
    text-align: center;
    color: var(--primary-colours-blue-navy);
  }

  .auth-content__form {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .ant-form-item {
      margin-bottom: 0;

      &:focus-visible {
        outline: none;
      }

      .ant-form-item-control-input-content {
        border-radius: 16px;
        background: #E7E5FF;
        box-shadow: 0px 4px 8px 0px #E7E5FF29;

        >input:-webkit-autofill {
          border-radius: 16px;
        }
      }

      .ant-input.ant-input-lg {
        padding: 15px 24px;
        background: none;
        border: none;
        box-shadow: unset;
        outline: unset;
        font-size: 16px;
        line-height: 24px;
      }

      input:-webkit-autofill {
        -webkit-box-shadow: 0 0 0px 1000px #E7E5FF inset !important;
      }

      .ant-input-affix-wrapper.ant-input-affix-wrapper-lg {
        background: none;
        border: none;
        box-shadow: unset;
        outline: unset;
        padding: 15px 24px;

        .ant-input.ant-input-lg {
          padding: 0;
        }
      }

      .ant-form-item-control .ant-form-item-explain .ant-form-item-explain-error {
        padding: 6px 16px;
        border-radius: 8px;
        margin: 4px 0 0 0;
        background: #FFDADA;
        color: #FF0307;
        font-size: 14px;
        line-height: 20px;
      }
    }
  }

  .auth-content__back-to {
    display: flex;
    align-items: center;
    gap: 4px;

    img {
      rotate: 90deg;
    }

    a {
      color: #313131;
      font-size: 14px;
      font-weight: 500;
      line-height: 18.2px;
      text-align: left;

      &:not(:hover):not(:active) {
        text-decoration: none;
      }
    }
  }

  .auth-content__redirect {
    display: flex;
    width: fit-content;
    align-self: center;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    text-align: center;
    color: #3A18CE;

    &:not(:hover):not(:active) {
      text-decoration: none;
    }

    .auth-form__checkbox {
      margin-right: 6px;
    }
  }

  .login-social {
    //background: red;
    margin-top: 24px;
    display: flex;
    justify-content: center;
  }

  .auth-content__submit {
    align-self: center;
    box-shadow: 0px 4px 8px 0px #E7E5FF29;
    border-radius: 16px;
    height: 54px;
  }

  .auth-content__question {
    text-align: center;
    color: var(--typo-colours-primary-black);
    line-height: 24px;

    .auth-content__question__redirect {
      font-weight: 600;
      color: #3A18CE;
      margin-left: 4px;

      &:not(:hover):not(:active) {
        text-decoration: none;
      }
    }
  }
}
import { useTranslation } from "react-i18next";
import AntButton from "@src/app/component/AntButton";
import { upperCaseFirstLetter } from "@src/common/dataConverter";
import { BUTTON, LANGUAGE, PROMOTION_TYPE } from "@constant";
import { Link } from "react-router-dom";
import { LINK } from "@link";
import { useMemo } from "react";


const PackageAddonItem = ({ packageData }) => {
  const { t, i18n } = useTranslation();
  const { name, prices, features } = packageData || {};

  const priceData = useMemo(() => {
    const originalPrice = prices[0];
    if (!originalPrice) return { ...prices[0], unitAmount: 0, promotionalPrice: 0 };
    const { promotionType, promotion } = originalPrice;
    const { unitAmount } = originalPrice || {};
    let promotionalPrice = unitAmount;
    if (unitAmount) {
      if (promotionType === PROMOTION_TYPE.PERCENTAGE.value) {
        promotionalPrice = unitAmount - (unitAmount * promotion / 100);
      } else if (promotionType === PROMOTION_TYPE.FIXED.value) {
        promotionalPrice = unitAmount - promotion;
      }
    }
    return { ...originalPrice, promotionalPrice: promotionalPrice };
  }, [prices]);

  const renderMoney = () => {
    const VND = new Intl.NumberFormat("vi-VN", {
      currency: "VND",
    });
    const { unitAmount, promotionalPrice } = priceData || {};
    return <>
      {+promotionalPrice !== +unitAmount && <del>{VND.format(Math.round(unitAmount))}</del>}
      <span className="price">{VND.format(Math.round(promotionalPrice))}</span>
      <span className="currency">{t("VND")}</span>
    </>
  }

  const renderFeature = () => {
    const { count } = features[0] || {};
    return `/ ${count} ${t("SUBMISSION_PRICING")}${count > 1 && i18n.language === LANGUAGE.EN ? "s" : ""}`;
  }

  return <div className="pricing-package-addon__item">
    <div className="pricing-package-base__addon__content">
      <div className="addon__content__name">{name}</div>

      <div className="item__content__info">
        <div className="item__content__info__price">{renderMoney()}</div>
        <div className="item__content__info__feature">{renderFeature()}</div>
      </div>
    </div>

    <Link to={LINK.PAYMENT_ID.format(packageData?._id)} state={{ ...priceData }}>
      <AntButton
        size="large"
        type={BUTTON.DEEP_GREEN}
      >
        {t("BUY_NOW")}
      </AntButton>
    </Link>


  </div >
};

export default PackageAddonItem;